import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/agent.dart';
import '../repositories/agents_repository.dart';

class GetAgentDetails implements UseCase<Agent, GetAgentDetailsParams> {
  final AgentsRepository repository;

  GetAgentDetails(this.repository);

  @override
  Future<Either<Failure, Agent>> call(GetAgentDetailsParams params) async {
    return await repository.getAgentDetails(params.agentId);
  }
}

class GetAgentDetailsParams extends Equatable {
  final String agentId;

  const GetAgentDetailsParams({required this.agentId});

  @override
  List<Object> get props => [agentId];
}
