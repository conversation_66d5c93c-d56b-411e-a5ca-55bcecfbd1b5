import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/property.dart';
import '../repositories/properties_repository.dart';

class GetProperties implements UseCase<List<Property>, GetPropertiesParams> {
  final PropertiesRepository repository;

  GetProperties(this.repository);

  @override
  Future<Either<Failure, List<Property>>> call(GetPropertiesParams params) async {
    return await repository.getProperties(
      type: params.type,
      status: params.status,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      minBedrooms: params.minBedrooms,
      maxBedrooms: params.maxBedrooms,
    );
  }
}

class GetPropertiesParams extends Equatable {
  final PropertyType? type;
  final PropertyStatus? status;
  final double? minPrice;
  final double? maxPrice;
  final int? minBedrooms;
  final int? maxBedrooms;

  const GetPropertiesParams({
    this.type,
    this.status,
    this.minPrice,
    this.maxPrice,
    this.minBedrooms,
    this.maxBedrooms,
  });

  @override
  List<Object?> get props => [
        type,
        status,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
      ];
}
