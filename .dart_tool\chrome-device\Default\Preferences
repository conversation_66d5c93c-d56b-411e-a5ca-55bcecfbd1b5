{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1420, "left": 735, "maximized": false, "right": 2000, "top": 48, "work_area_bottom": 1392, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 16709, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "7105ba94-cfaf-42f8-a523-6d36cd33d696", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.158"}, "gaia_cookie": {"changed_time": **********.629059, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "ac90ec10-26e6-418f-8e9b-62964dc47ea5"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-GB,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "7kHQgDbWgQcqqjMYoWt4cRPF/zp99iFOGLsDijR1xolXbWG68qvfJ8ao8JB/R8h/qAU7I9UqiRDNXNbXVR5+eQ=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13397479865549101", "last_fetch_success": "13397479866440101"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:50213,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60568,*": {"expiration": "13405255912097101", "last_modified": "13397479912097101", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:8080,*": {"expiration": "13405193458790387", "last_modified": "13397417458790387", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:50213,*": {"last_modified": "13397479855744632", "setting": {"lastEngagementTime": 1.3397392841359428e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "http://localhost:60568,*": {"last_modified": "13397479902203101", "setting": {"lastEngagementTime": 1.33974799022031e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:8080,*": {"last_modified": "13397479855744632", "setting": {"lastEngagementTime": 1.3397451055744632e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 14.6626062802944}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.158", "creation_time": "13397308830445409", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13397479902203101", "last_time_obsolete_http_credentials_removed": 1752835290.493059, "last_time_password_store_metrics_reported": 1752943153.415212, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13397568031672059", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13397395230", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EITInMiSnOYXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEITInMiSnOYXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ+5T70ICZ5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEPuU+9CAmeYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397356799000000", "uma_in_sql_start_time": "13397308830492409"}, "sessions": {"event_log": [{"crashed": false, "time": "13397308830489409", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397308976663995", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397387582129403", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397416031974644", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397416723413745", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397417458771387", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397479855557632", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397479912077101", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}