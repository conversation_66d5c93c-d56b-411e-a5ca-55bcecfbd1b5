<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PROPIMATCH AI - Professional Prototype</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .chat-messages::-webkit-scrollbar { width: 6px; }
        .chat-messages::-webkit-scrollbar-track { background: #f1f5f9; }
        .chat-messages::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 3px; }
        .chat-messages::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
        
        /* New PROPIMATCH Theme */
        :root {
            --brand-orange: #DD6B20;
            --brand-orange-dark: #C05621;
        }

        /* Sidebar Navigation */
        #sidebar .nav-item { transition: all 0.2s ease-in-out; }
        #sidebar .nav-item:hover { background-color: #f1f5f9; color: #312e81; }
        #sidebar .nav-item.active { background-color: #FFF5EB; color: var(--brand-orange-dark); font-weight: 600; }

        /* Bottom Tab Bar Navigation */
        #bottom-nav .nav-item { color: #64748b; transition: color 0.2s ease-in-out; }
        #bottom-nav .nav-item.active { color: var(--brand-orange); font-weight: 600; }

        .leaflet-tile-pane { z-index: 1; }
        .leaflet-popup-content-wrapper { border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
        
        .btn-primary {
            background-color: var(--brand-orange);
            color: white;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: var(--brand-orange-dark);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.07), 0 2px 4px -2px rgb(0 0 0 / 0.07);
            transition: all 0.2s ease-in-out;
        }
        .card-shadow:hover, .card-shadow.highlighted {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .boosted-card {
             border-color: var(--brand-orange);
             box-shadow: 0 0 15px rgba(221, 107, 32, 0.3);
        }
        .gemini-btn {
            background: linear-gradient(90deg, var(--brand-orange), #f97316);
            color: white;
        }
        
        /* Toggle Switch */
        .switch { position: relative; display: inline-block; width: 40px; height: 24px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 16px; width: 16px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: var(--brand-orange); }
        input:checked + .slider:before { transform: translateX(16px); }

        /* Star Rating */
        .star-rating { display: flex; flex-direction: row-reverse; justify-content: center; }
        .star-rating input { display: none; }
        .star-rating label { font-size: 2rem; color: #ddd; cursor: pointer; transition: color 0.2s; }
        .star-rating input:checked ~ label,
        .star-rating label:hover,
        .star-rating label:hover ~ label { color: #f59e0b; }
        
    </style>
</head>
<body class="text-slate-800">

    <div id="modal-container" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4"></div>

    <div id="login-screen" class="min-h-screen flex items-center justify-center bg-slate-100 p-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 space-y-6">
            <div class="text-center">
                <div class="mb-8">
                    <svg class="h-28 w-28 text-orange-600 mx-auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                        <path d="M3 9.5L12 4L21 9.5V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9.5Z" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 22V12C9 11.4696 9.21071 10.9609 9.58579 10.5858C9.96086 10.2107 10.4696 10 11 10H13C13.5304 10 14.0391 10.2107 14.4142 10.5858C14.7893 10.9609 15 11.4696 15 12V22" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <img src="https://i.imgur.com/uIeM52i.png" alt="PROPIMATCH Logo" class="h-10 mx-auto mb-4"/>
                <p class="text-slate-600">Your Real Estate AI Companion</p>
            </div>
            <div id="login-error" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative" role="alert">
                <strong class="font-bold">Error:</strong>
                <span class="block sm:inline">Invalid credentials. Please try again.</span>
            </div>
            <form id="login-form" class="space-y-6">
                <div>
                    <label for="email" class="text-sm font-medium text-slate-700">Email address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required class="mt-1 block w-full px-3 py-2 bg-white border border-slate-300 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm">
                </div>
                <div>
                    <label for="password" class="text-sm font-medium text-slate-700">Password</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required class="mt-1 block w-full px-3 py-2 bg-white border border-slate-300 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm">
                </div>
                <div><button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm btn-primary">Sign in</button></div>
                <div class="relative">
                    <div class="absolute inset-0 flex items-center" aria-hidden="true">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Quick Access</span>
                    </div>
                </div>
                <div id="quick-access-buttons" class="grid grid-cols-2 gap-3">
                    <button type="button" data-email="<EMAIL>" class="quick-login-btn w-full bg-slate-100 text-slate-700 text-xs font-semibold py-2 px-2 rounded-md hover:bg-slate-200">Admin</button>
                    <button type="button" data-email="<EMAIL>" class="quick-login-btn w-full bg-slate-100 text-slate-700 text-xs font-semibold py-2 px-2 rounded-md hover:bg-slate-200">User</button>
                    <button type="button" data-email="<EMAIL>" class="quick-login-btn w-full bg-slate-100 text-slate-700 text-xs font-semibold py-2 px-2 rounded-md hover:bg-slate-200">Investor</button>
                    <button type="button" data-email="<EMAIL>" class="quick-login-btn w-full bg-slate-100 text-slate-700 text-xs font-semibold py-2 px-2 rounded-md hover:bg-slate-200">Agent (Verified)</button>
                    <button type="button" data-email="<EMAIL>" class="quick-login-btn w-full bg-slate-100 text-slate-700 text-xs font-semibold py-2 px-2 rounded-md hover:bg-slate-200 col-span-2">Agent (Pending)</button>
                </div>
            </form>
        </div>
    </div>

    <div id="app-screen" class="hidden">
        <div class="relative h-screen flex flex-col md:flex-row">
            <nav id="sidebar" class="w-64 bg-white border-r border-slate-200 flex-col flex-shrink-0 hidden md:flex z-20">
                <div class="flex items-center justify-center p-4 border-b border-slate-200 h-16">
                    <img src="https://i.imgur.com/uIeM52i.png" alt="PROPIMATCH Logo" class="h-8"/>
                </div>
                <div id="sidebar-nav-links" class="flex-grow p-4 space-y-2"></div>
                <div id="sidebar-footer" class="p-4 border-t border-slate-200">
                    <div id="user-info" class="flex items-center space-x-3">
                         <img id="user-info-img" class="w-10 h-10 rounded-full object-cover">
                         <div class="flex-grow"><p id="user-info-name" class="font-semibold text-sm text-slate-800"></p><p id="user-info-role" class="text-xs text-slate-500"></p></div>
                        <button id="sidebar-logout-button" class="p-2 rounded-full text-slate-500 hover:bg-slate-100 hover:text-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75" /></svg></button>
                    </div>
                </div>
            </nav>

            <div class="flex-1 flex flex-col overflow-hidden">
                <main id="main-content" class="flex-1 overflow-y-auto p-4 md:p-6 lg:p-10"></main>

                <nav id="bottom-nav" class="flex-shrink-0 bg-white border-t border-slate-200 flex justify-around md:hidden z-30">
                    </nav>
            </div>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script>
        // --- MOCK DATABASE & API SIMULATION ---
        let mockDatabase = {
            users: {
                "<EMAIL>": { uid: "adminId", name: "Admin User", phone: "******-0100", location: "Platform HQ", email: "<EMAIL>", userRole: "admin", status: "Active", imageUrl: "https://images.pexels.com/photos/839011/pexels-photo-839011.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" },
                "<EMAIL>": { uid: "userId2", name: "Olivia Investor", phone: "******-0102", location: "New York, USA", email: "<EMAIL>", userRole: "investor", recentlyViewed: [], status: "Active", credits: 20, imageUrl: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" },
                "<EMAIL>": { uid: "userId1", name: "John Doe", phone: "******-0101", location: "Dubai, UAE", email: "<EMAIL>", userRole: "regular", recentlyViewed: [], status: "Active", imageUrl: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" },
                "<EMAIL>": { uid: "agentId1", name: "Pending Agent", phone: "******-0103", location: "London, UK", email: "<EMAIL>", userRole: "agent", status: "Active", verificationStatus: "Pending", recentlyViewed: [], imageUrl: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" },
                "<EMAIL>": { uid: "agentId2", name: "Omar Hassan", phone: "+971-55-123-4567", location: "Dubai, UAE", email: "<EMAIL>", userRole: "agent", status: "Active", verificationStatus: "Verified", recentlyViewed: [], credits: 10, imageUrl: "https://images.pexels.com/photos/842980/pexels-photo-842980.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1", specialization: ["Luxury", "Waterfront"], languages: ["Arabic", "English"], rating: 4.9, experience: 8, propertiesSold: 112 },
                "<EMAIL>": { uid: "userId3", name: "Suspended User", phone: "******-0104", location: "Sydney, AU", email: "<EMAIL>", userRole: "regular", recentlyViewed: [], status: "Suspended", imageUrl: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" },
                "<EMAIL>": { uid: "agentId3", name: "Sarah Khan", phone: "+971-50-789-1234", location: "Dubai Marina", email: "<EMAIL>", userRole: "agent", status: "Active", verificationStatus: "Verified", recentlyViewed: [], credits: 25, imageUrl: "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1", specialization: ["Apartments", "High-rise"], languages: ["English", "Urdu"], rating: 4.8, experience: 5, propertiesSold: 89 },
                "<EMAIL>": { uid: "agentId4", name: "Ahmed Hassan", phone: "+971-52-555-6789", location: "Jumeirah Beach Residence", email: "<EMAIL>", userRole: "agent", status: "Active", verificationStatus: "Verified", recentlyViewed: [], credits: 15, imageUrl: "https://images.pexels.com/photos/837358/pexels-photo-837358.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1", specialization: ["Villas", "Family Homes"], languages: ["Arabic", "English", "French"], rating: 4.7, experience: 12, propertiesSold: 150 }
            },
            properties: [
                { propertyId: "prop1", address: "123 Main St, Anytown, USA", type: "House", status: "sale", price: 350000, bedrooms: 3, bathrooms: 2, sqft: 1500, amenities: ["Swimming Pool", "Gym", "Covered Parking"], predictedValueGrowth_1yr: "high", investmentPotentialScore: 8.5, imageUrls: ['https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1', 'https://images.pexels.com/photos/209296/pexels-photo-209296.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'], coords: [34.05, -118.24], summary: ["✅ Priced 3% below market average.", "📈 High investment potential.", "🏡 Quiet, family-friendly street."], verificationStatus: "Verified", videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", valuationHistory: [{date:'Jan 2023',value:320000},{date:'Apr 2023',value:335000},{date:'Jul 2023',value:340000},{date:'Oct 2023',value:345000},{date:'Jan 2024',value:350000}], ownerId: "agentId2", isBoosted: true, views: 1250, inquiries: 45, soldDate: null, valueForMoneyScore: 'High', demandScore: 'High' },
                { propertyId: "prop2", address: "456 Oak Ave, Suburbia, Canada", type: "Condo", status: "rent", price: 2200, bedrooms: 2, bathrooms: 2, sqft: 1100, amenities: ["Gym", "24/7 Security"], predictedValueGrowth_1yr: "medium", investmentPotentialScore: 7.2, imageUrls: ['https://images.pexels.com/photos/276724/pexels-photo-276724.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'], coords: [45.42, -75.69], summary: ["✅ Includes gym and pool access.", "🚇 10-minute walk to metro station.", "⚠️ High service charge reported in area."], verificationStatus: "Verified", videoUrl: "", valuationHistory: [{date:'Jan 2023',value:2000},{date:'Apr 2023',value:2100},{date:'Jul 2023',value:2150},{date:'Oct 2023',value:2180},{date:'Jan 2024',value:2200}], ownerId: "agentId2", isBoosted: false, views: 800, inquiries: 20, soldDate: '2025-03-15', valueForMoneyScore: 'Medium', demandScore: 'High' },
            ],
            investorProperties: [
                { propertyId: "invProp1", address: "987 Vista Blvd, Hillside, USA", type: "House", status: "sale", price: 780000, bedrooms: 5, bathrooms: 4, sqft: 3200, amenities: ["Swimming Pool", "Gym", "Covered Parking", "24/7 Security"], predictedValueGrowth_1yr: "high", investmentPotentialScore: 9.3, imageUrls: ['https://images.pexels.com/photos/164558/pexels-photo-164558.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'], coords: [34.08, -118.20], summary: ["✅ Panoramic city views.", "💰 Strong rental income history."], verificationStatus: "Verified", videoUrl: "", valuationHistory: [{date:'Jan 2023',value:720000},{date:'Apr 2023',value:745000},{date:'Jul 2023',value:760000},{date:'Oct 2023',value:770000},{date:'Jan 2024',value:780000}], ownerId: "userId2", isBoosted: false, views: 2500, inquiries: 80, soldDate: null, valueForMoneyScore: 'Medium', demandScore: 'Low' },
            ],
            pendingProperties: [
                 { propertyId: "prop3", address: "789 Pine Ln, Downtown, UK", type: "Townhouse", status: "sale", price: 450000, bedrooms: 2, bathrooms: 3, sqft: 1300, amenities: ["Covered Parking"], predictedValueGrowth_1yr: "high", investmentPotentialScore: 9.1, imageUrls: ['https://images.pexels.com/photos/208736/pexels-photo-208736.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'], coords: [51.50, -0.12], summary: ["✅ Prime downtown location.", "✨ Recently renovated kitchen.", "🅿️ Limited guest parking."], verificationStatus: "Pending", videoUrl: "", valuationHistory: [{date:'Jan 2023',value:410000},{date:'Apr 2023',value:425000},{date:'Jul 2023',value:435000},{date:'Oct 2023',value:440000},{date:'Jan 2024',value:450000}], ownerId: "agentId2", isBoosted: false, views: 0, inquiries: 0, soldDate: null, valueForMoneyScore: 'High', demandScore: 'Medium' },
                 { propertyId: "invProp2", address: "654 Market St, Downtown, UK", type: "Commercial", status: "rent", price: 8500, bedrooms: 0, bathrooms: 2, sqft: 2500, amenities: ["24/7 Security"], predictedValueGrowth_1yr: "high", investmentPotentialScore: 9.5, imageUrls: ['https://images.pexels.com/photos/262353/pexels-photo-262353.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'], coords: [51.52, -0.11], summary: ["✅ High foot traffic location.", "🏢 Suitable for retail or office space."], verificationStatus: "Pending", videoUrl: "", valuationHistory: [{date:'Jan 2023',value:8000},{date:'Apr 2023',value:8200},{date:'Jul 2023',value:8350},{date:'Oct 2023',value:8400},{date:'Jan 2024',value:8500}], ownerId: "userId2", isBoosted: false, views: 0, inquiries: 0, soldDate: null, valueForMoneyScore: 'Low', demandScore: 'High' }
            ],
            leads: [
                { leadId: 'lead1', agentId: 'agentId2', userId: 'userId1', userEmail: '<EMAIL>', message: 'Hi, I saw your listing for 123 Main St and would like to schedule a viewing.', status: 'accepted', date: '2025-07-08T10:00:00Z' },
                { leadId: 'lead2', agentId: 'agentId2', userId: 'userId1', userEmail: '<EMAIL>', message: 'I am very interested in the condo on Oak Ave. Is it still available for rent?', status: 'new', date: '2025-07-09T11:30:00Z' },
                { leadId: 'lead3', agentId: 'agentId2', userId: 'userId1', userEmail: '<EMAIL>', message: 'Could you provide more details on the townhouse? Thanks.', status: 'rejected', date: '2025-06-20T15:00:00Z' },
            ],
            reviews: [
                { reviewId: 'rev1', agentId: 'agentId2', userId: 'userId1', rating: 4.9, text: 'Fantastic agent! Very helpful and knowledgeable about the market.', date: '2025-07-09' }
            ],
            paymentHistory: {
                "agentId2": [
                    { date: '2025-07-15', amount: 250, status: 'Paid'},
                    { date: '2025-07-10', amount: 100, status: 'Paid'},
                    { date: '2025-07-05', amount: 500, status: 'Paid'},
                ]
            },
            notifications: {
                "userId1": [
                    { id: 'notif1', type: 'match', title: 'New Property Match!', text: 'A new property matching your criteria has been listed.', date: '2025-07-12', read: false },
                    { id: 'notif2', type: 'chat', title: 'New Chat Reply', text: 'You have a new message from Omar Hassan.', date: '2025-07-11', read: false },
                    { id: 'notif3', type: 'reminder', title: 'Viewing Reminder', text: 'Your viewing for 123 Main St is tomorrow at 2 PM.', date: '2025-07-10', read: true },
                ]
            },
            featureFlags: {
                'dashboard': { name: 'Dashboard', enabled: true },
                'advisor': { name: 'AI Property Finder', enabled: true },
                'valuation': { name: 'Valuation Tool', enabled: true },
                'manage': { name: 'Manage Properties', enabled: true },
                'find_agent': { name: 'Find an Agent', enabled: true },
                'agent_leads': { name: 'My Leads', enabled: true }
            }
        };

        // --- APPLICATION LOGIC ---
        document.addEventListener('DOMContentLoaded', () => {
            const loginScreen = document.getElementById('login-screen');
            const appScreen = document.getElementById('app-screen');
            const loginForm = document.getElementById('login-form');
            const logoutButton = document.getElementById('sidebar-logout-button');
            const loginError = document.getElementById('login-error');
            const mainContent = document.getElementById('main-content');
            const sidebarNavLinksContainer = document.getElementById('sidebar-nav-links');
            const bottomNavContainer = document.getElementById('bottom-nav');
            const modalContainer = document.getElementById('modal-container');

            let currentUser;
            let mapInstance;
            let mapMarkers = [];
            let currentFilteredProperties = [];
            let contextualProperty = null;
            let uploadedImageFiles = [];
            let uploadedDocFiles = [];
            let activeCharts = [];
            let currentListingView = 'list';

            const navConfig = {
                 user: [
                    { page: 'dashboard', flag: 'dashboard', label: 'Dashboard', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>' },
                    { page: 'advisor', flag: 'advisor', label: 'Finder', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>' },
                    { page: 'find_agent', flag: 'find_agent', label: 'Agents', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" /></svg>'},
                    { page: 'notifications', label: 'Notifications', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>'},
                    { page: 'profile', label: 'Profile', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>'}
                ],
                investor: [
                    { page: 'investor_dashboard', flag: 'dashboard', label: 'Dashboard', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>' },
                    { page: 'manage', flag: 'manage', label: 'Properties', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" /></svg>' },
                    { page: 'notifications', label: 'Notifications', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>'},
                    { page: 'profile', label: 'Profile', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>'}
                ],
                agent: [
                    { page: 'agent_dashboard', flag: 'dashboard', label: 'Dashboard', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>' },
                    { page: 'agent_leads', flag: 'agent_leads', label: 'Leads', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3h3m-3 4h3m-3 4h3m-3 4h3" /></svg>'},
                    { page: 'advisor', flag: 'advisor', label: 'Finder', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>' },
                    { page: 'manage', flag: 'manage', label: 'Listings', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" /></svg>' },
                    { page: 'profile', label: 'Profile', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>'}
                ],
                admin: [
                    { page: 'feature_control', label: 'Features', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>' },
                    { page: 'user_management', label: 'Users', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" /></svg>' },
                    { page: 'agent_verification', label: 'Agents', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>' },
                    { page: 'property_verification', label: 'Properties', icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3z" /></svg>'}
                ]
            };
            
            function buildNav(userRole, verificationStatus = '') {
                let links = [];
                // Determine the correct set of navigation links based on user role
                if (userRole === 'admin') {
                    links = [...navConfig.admin];
                } else if (userRole === 'agent') {
                    if (verificationStatus === 'Verified') {
                        links = navConfig.agent.filter(link => !link.flag || mockDatabase.featureFlags[link.flag]?.enabled);
                    }
                } else { // regular and investor
                    let baseLinks = navConfig.user.filter(link => !link.flag || mockDatabase.featureFlags[link.flag]?.enabled);
                    if (userRole === 'investor') {
                        // Replace regular dashboard with investor dashboard and add investor-specific links
                        baseLinks = baseLinks.filter(l => l.page !== 'dashboard');
                        links.push(...navConfig.investor.filter(link => !link.flag || mockDatabase.featureFlags[link.flag]?.enabled));
                    }
                    links.push(...baseLinks);
                }

                // Populate Sidebar (Web)
                sidebarNavLinksContainer.innerHTML = links.map(link => `<a href="#" data-page="${link.page}" class="nav-item flex items-center space-x-3 px-4 py-2.5 text-slate-600 rounded-lg"><span>${link.icon}</span><span>${link.label}</span></a>`).join('');
                
                // Populate Bottom Nav (Mobile)
                bottomNavContainer.innerHTML = links.slice(0, 5).map(link => `<a href="#" data-page="${link.page}" class="nav-item text-center p-2 pt-3 flex-grow"><div class="flex justify-center">${link.icon}</div><span class="text-xs mt-1 block">${link.label}</span></a>`).join('');

                // Add event listeners to all nav items
                document.querySelectorAll('.nav-item').forEach(item => item.addEventListener('click', (e) => { 
                    e.preventDefault(); 
                    navigateTo(item.dataset.page, item.dataset.id); 
                }));
            }

            function navigateTo(page, id = null) {
                activeCharts.forEach(chart => chart.destroy());
                activeCharts = [];
                if (mapInstance) { mapInstance.remove(); mapInstance = null; }

                // Toggle active class on both sidebar and bottom nav items
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.page === page);
                });

                mainContent.innerHTML = '';
                mainContent.classList.remove('fade-in');
                void mainContent.offsetWidth;
                mainContent.classList.add('fade-in');
                
                // Reset main content styles, then apply page-specific ones
                mainContent.className = 'flex-1 overflow-y-auto p-4 md:p-6 lg:p-10';
                mainContent.style.height = '';

                if (page === 'advisor') {
                    // Use different padding and height for the advisor page for the chat layout
                    mainContent.className = 'flex-1 flex flex-col overflow-hidden p-0';
                }

                switch (page) {
                    case 'dashboard': renderDashboardPage(currentUser); break;
                    case 'investor_dashboard': renderInvestorDashboardPage(); break;
                    case 'agent_dashboard': renderAgentDashboardPage(); break;
                    case 'advisor': renderPropertyFinderPage(); break;
                    case 'valuation': renderValuationPage(); break;
                    case 'manage': renderManagePropertiesPage(); break;
                    case 'feature_control': renderFeatureControlPage(); break;
                    case 'user_management': renderUserManagementPage(); break;
                    case 'agent_verification': renderAgentVerificationPage(); break;
                    case 'property_verification': renderPropertyVerificationPage(); break;
                    case 'find_agent': renderFindAgentPage(); break;
                    case 'agent_profile': renderAgentProfilePage(id); break;
                    case 'agent_leads': renderAgentLeadsPage(); break;
                    case 'profile': renderProfilePage(); break;
                    case 'payments': renderPaymentsPage(); break;
                    case 'notifications': renderNotificationsPage(); break;
                    default: renderDashboardPage(currentUser);
                }
            }

            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                loginError.classList.add('hidden');
                const user = mockDatabase.users[document.getElementById('email').value];
                if (user && user.status === 'Active' && document.getElementById('password').value === 'password') {
                    currentUser = user;
                    sessionStorage.setItem('loggedInUser', JSON.stringify(user));
                    showApp();
                } else {
                    loginError.textContent = user?.status === 'Suspended' ? 'This account has been suspended.' : 'Invalid credentials. Please try again.';
                    loginError.classList.remove('hidden');
                }
            });
            
            document.querySelectorAll('.quick-login-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const email = e.target.dataset.email;
                    document.getElementById('email').value = email;
                    document.getElementById('password').value = 'password';
                    loginForm.dispatchEvent(new Event('submit'));
                });
            });

            logoutButton.addEventListener('click', () => {
                currentUser = null;
                sessionStorage.removeItem('loggedInUser');
                appScreen.classList.add('hidden');
                loginScreen.classList.remove('hidden');
            });

            const loggedInUser = JSON.parse(sessionStorage.getItem('loggedInUser'));
            if (loggedInUser) {
                currentUser = loggedInUser;
                showApp();
            } else {
                loginScreen.classList.remove('hidden');
            }

            function showApp() {
                loginScreen.classList.add('hidden');
                appScreen.classList.remove('hidden');
                document.getElementById('user-info-img').src = currentUser.imageUrl;
                document.getElementById('user-info-name').textContent = currentUser.name;
                document.getElementById('user-info-role').textContent = `Role: ${currentUser.userRole.charAt(0).toUpperCase() + currentUser.userRole.slice(1)}`;
                buildNav(currentUser.userRole, currentUser.verificationStatus);
                
                if (currentUser.userRole === 'agent' && currentUser.verificationStatus === 'Pending') {
                    renderAgentPendingPage();
                } else if (currentUser.userRole === 'admin') {
                    navigateTo('feature_control');
                } else if (currentUser.userRole === 'agent') {
                    navigateTo('agent_dashboard');
                } else if (currentUser.userRole === 'investor') {
                    navigateTo('investor_dashboard');
                }
                 else {
                    navigateTo('dashboard');
                }
            }
            
            function renderDashboardPage(user) {
                let content = `<div class="lg:col-span-2 space-y-8">${createRecommendedComponent(user)}</div>
                               <div class="lg:col-span-1 space-y-8">${createRecentlyViewedComponent(user)}</div>`;
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Welcome Home</h1><div class="grid grid-cols-1 lg:grid-cols-3 gap-8">${content}</div>`;
            }

            function renderAgentPendingPage() {
                mainContent.innerHTML = `<div class="text-center p-10 bg-white rounded-2xl shadow-lg"><h1 class="text-3xl font-extrabold text-slate-900 mb-4">Application Pending</h1><p class="text-slate-600">Thank you for registering. Your application is currently under review by our team. You will be notified once it's approved.</p></div>`;
            }
            
            function renderValuationPage() {
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Dynamic Property Valuation</h1><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="bg-white p-8 rounded-2xl shadow-lg border border-slate-200"><h3 class="text-xl font-bold mb-4">Enter Property Details</h3><form id="valuation-form" class="space-y-4"><input type="text" placeholder="Address (e.g., 123 Main St)" required class="w-full p-3 border border-slate-300 rounded-md"><input type="number" placeholder="Square Feet (e.g., 1500)" required class="w-full p-3 border border-slate-300 rounded-md"><input type="number" placeholder="Bedrooms (e.g., 3)" required class="w-full p-3 border border-slate-300 rounded-md"><input type="number" placeholder="Bathrooms (e.g., 2)" required class="w-full p-3 border border-slate-300 rounded-md"><button type="submit" class="w-full btn-primary font-semibold py-3 px-4 rounded-lg">Get Valuation</button></form></div><div id="valuation-result" class="bg-white p-8 rounded-2xl shadow-lg border border-slate-200 flex items-center justify-center"><p class="text-slate-500">Your valuation result will appear here.</p></div></div>`;
                document.getElementById('valuation-form').addEventListener('submit', e => {
                    e.preventDefault();
                    const resultContainer = document.getElementById('valuation-result');
                    resultContainer.innerHTML = `<div class="text-center"><p class="font-semibold">Calculating...</p><div class="mt-2 w-16 h-16 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto"></div></div>`;
                    setTimeout(() => {
                        const mockValue = 350000 + Math.floor(Math.random() * 100000);
                        resultContainer.innerHTML = `<div class="text-center w-full fade-in"><p class="text-slate-500 text-sm">AI-Powered Estimate</p><p class="text-5xl font-extrabold text-slate-900 my-2">$${mockValue.toLocaleString()}</p><p class="text-green-600 font-semibold">High Confidence</p><div class="mt-6 h-48"><canvas id="valuationResultChart"></canvas></div></div>`;
                        const chartData = mockDatabase.properties[0].valuationHistory.map(h => ({...h, value: h.value * (mockValue/350000)}));
                        renderChart(document.getElementById('valuationResultChart'), 'line', { labels: chartData.map(d => d.date), datasets: [{ label: 'Estimated Value', data: chartData.map(d => d.value), borderColor: '#DD6B20', backgroundColor: '#DD6B2020', fill: true, tension: 0.4 }]});
                    }, 1500);
                });
            }

            function renderManagePropertiesPage() {
                mainContent.innerHTML = `<div class="flex justify-between items-center mb-6">
                                            <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900">Manage Properties</h1>
                                            <div id="view-toggles" class="hidden md:flex items-center gap-2 bg-slate-200 p-1 rounded-lg">
                                                <button data-view="list" class="view-toggle-btn active bg-white text-slate-800 rounded-md px-3 py-1 text-sm font-semibold">List</button>
                                                <button data-view="table" class="view-toggle-btn text-slate-600 rounded-md px-3 py-1 text-sm font-semibold">Table</button>
                                            </div>
                                         </div>
                                         <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                                            <div class="lg:col-span-1 sticky top-8">
                                                <div id="property-form-container" class="bg-white p-8 rounded-2xl shadow-lg border border-slate-200"></div>
                                            </div>
                                            <div id="user-listings-container" class="lg:col-span-2"></div>
                                         </div>`;
                renderPropertyForm();
                displayUserListings();

                document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.view-toggle-btn').forEach(b => b.classList.remove('active', 'bg-white', 'text-slate-800'));
                        e.target.classList.add('active', 'bg-white', 'text-slate-800');
                        currentListingView = e.target.dataset.view;
                        displayUserListings();
                    });
                });
            }

            function renderPropertyForm(property = null) {
                const isEditing = property !== null;
                const formHtml = `
                    <h3 class="text-xl font-bold mb-4">${isEditing ? 'Edit Property' : 'Add New Property'}</h3>
                    <form id="add-property-form" data-editing-id="${isEditing ? property.propertyId : ''}" class="space-y-4">
                        <input name="address" type="text" placeholder="Address" required class="w-full p-3 border border-slate-300 rounded-md" value="${isEditing ? property.address : ''}">
                        <div>
                            <label class="text-sm font-medium text-slate-700">Property Images</label>
                            <div id="image-preview-container" class="mt-2 grid grid-cols-3 gap-2"></div>
                            <button type="button" id="upload-btn" class="mt-2 w-full bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-2 px-4 rounded-md">Upload Images</button>
                            <input id="image-upload" type="file" accept="image/*" class="hidden" multiple>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-slate-700">Verification Documents</label>
                            <div id="doc-preview-container" class="mt-2 space-y-2"></div>
                            <button type="button" id="doc-upload-btn" class="mt-2 w-full bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-2 px-4 rounded-md">Upload Document(s)</button>
                            <input id="doc-upload" type="file" class="hidden" multiple>
                        </div>
                        <select name="status" class="w-full p-3 border border-slate-300 rounded-md"><option value="sale" ${isEditing && property.status === 'sale' ? 'selected' : ''}>For Sale</option><option value="rent" ${isEditing && property.status === 'rent' ? 'selected' : ''}>For Rent</option></select>
                        <input name="price" type="number" placeholder="Price / Rent per month" required class="w-full p-3 border border-slate-300 rounded-md" value="${isEditing ? property.price : ''}">
                        <div class="grid grid-cols-3 gap-4">
                            <input name="bedrooms" type="number" placeholder="Beds" required class="w-full p-3 border border-slate-300 rounded-md" value="${isEditing ? property.bedrooms : ''}"><input name="bathrooms" type="number" placeholder="Baths" required class="w-full p-3 border border-slate-300 rounded-md" value="${isEditing ? property.bathrooms : ''}"><input name="sqft" type="number" placeholder="Sqft" required class="w-full p-3 border border-slate-300 rounded-md" value="${isEditing ? property.sqft : ''}">
                        </div>
                        <textarea id="summary-textarea" name="summary" placeholder="AI Generated Summary will appear here..." class="w-full p-3 border border-slate-300 rounded-md" rows="4">${isEditing && property.summary ? property.summary.join('\\n') : ''}</textarea>
                        <button type="button" id="gemini-summary-btn" class="w-full gemini-btn font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2">✨ Generate AI Summary</button>
                        <div class="flex gap-4">
                            <button type="submit" class="w-full btn-primary font-semibold py-3 px-4 rounded-lg">${isEditing ? 'Save Changes' : 'Submit for Approval'}</button>
                            ${isEditing ? '<button type="button" id="cancel-edit-btn" class="w-full bg-slate-200 text-slate-800 font-semibold py-3 px-4 rounded-lg hover:bg-slate-300">Cancel</button>' : ''}
                        </div>
                    </form>
                `;
                document.getElementById('property-form-container').innerHTML = formHtml;
                
                document.getElementById('upload-btn').addEventListener('click', () => document.getElementById('image-upload').click());
                document.getElementById('image-upload').addEventListener('change', handleImageUpload);
                document.getElementById('doc-upload-btn').addEventListener('click', () => document.getElementById('doc-upload').click());
                document.getElementById('doc-upload').addEventListener('change', handleDocUpload);
                document.getElementById('add-property-form').addEventListener('submit', handlePropertyFormSubmit);
                document.getElementById('gemini-summary-btn').addEventListener('click', handleGenerateSummary);
                if (isEditing) {
                    document.getElementById('cancel-edit-btn').addEventListener('click', () => renderPropertyForm());
                    if (property.imageUrls) {
                        uploadedImageFiles = [...property.imageUrls];
                        updateImagePreview();
                    }
                }
            }

            function handleImageUpload(e) {
                const files = e.target.files;
                if (files) {
                    for (const file of files) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            uploadedImageFiles.push(event.target.result);
                            updateImagePreview();
                        }
                        reader.readAsDataURL(file);
                    }
                }
            }

            function handleDocUpload(e) {
                const files = e.target.files;
                if (files) {
                    for (const file of files) {
                        uploadedDocFiles.push(file);
                    }
                    updateDocPreview();
                }
            }

            function updateImagePreview() {
                const container = document.getElementById('image-preview-container');
                container.innerHTML = '';
                uploadedImageFiles.forEach((src, index) => {
                    const imgWrapper = document.createElement('div');
                    imgWrapper.className = 'relative';
                    imgWrapper.innerHTML = `
                        <img src="${src}" class="w-full h-20 object-cover rounded-md">
                        <button type="button" data-index="${index}" class="remove-img-btn absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-4 h-4 text-xs flex items-center justify-center">×</button>
                    `;
                    container.appendChild(imgWrapper);
                });

                document.querySelectorAll('.remove-img-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const index = parseInt(e.target.dataset.index);
                        uploadedImageFiles.splice(index, 1);
                        updateImagePreview();
                    });
                });
            }

            function updateDocPreview() {
                const container = document.getElementById('doc-preview-container');
                container.innerHTML = '';
                uploadedDocFiles.forEach((file, index) => {
                    const docWrapper = document.createElement('div');
                    docWrapper.className = 'flex items-center justify-between bg-slate-100 p-2 rounded-md text-sm';
                    docWrapper.innerHTML = `
                        <span class="truncate">${file.name}</span>
                        <button type="button" data-index="${index}" class="remove-doc-btn text-red-500 font-bold">×</button>
                    `;
                    container.appendChild(docWrapper);
                });
                document.querySelectorAll('.remove-doc-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const index = parseInt(e.target.dataset.index);
                        uploadedDocFiles.splice(index, 1);
                        updateDocPreview();
                    });
                });
            }
            
            async function handleGenerateSummary() {
                const btn = document.getElementById('gemini-summary-btn');
                const textarea = document.getElementById('summary-textarea');
                const form = document.getElementById('add-property-form');
                const formData = new FormData(form);
                const propertyData = Object.fromEntries(formData.entries());

                if (!propertyData.address || !propertyData.price || !propertyData.sqft) {
                    showModal("Info", "Please fill in at least the address, price, and square feet before generating a summary.");
                    return;
                }

                const promptDetails = `- Address: ${propertyData.address}\n- Type: ${propertyData.status === 'sale' ? 'For Sale' : 'For Rent'}\n- Price: $${parseInt(propertyData.price).toLocaleString()}\n- Specs: ${propertyData.bedrooms} beds, ${propertyData.bathrooms} baths, ${propertyData.sqft} sqft`;
                const prompt = `Generate a concise, appealing real estate summary for a property with these details:\n${promptDetails}\n\nThe summary should be exactly 3 short, punchy bullet points. Each bullet point must start with a relevant emoji (like ✅, ✨, 🏡, 💰, 📈). Do not include any introductory text, just the bullet points.`;

                btn.disabled = true;
                btn.innerHTML = `<div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div><span>Generating...</span>`;
                textarea.value = "AI is thinking...";

                try {
                    let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
                    const payload = { contents: chatHistory };
                    const apiKey = ""; 
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                    
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) throw new Error(`API request failed with status ${response.status}`);

                    const result = await response.json();
                    
                    if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {
                        const text = result.candidates[0].content.parts[0].text;
                        textarea.value = text.split('\n').filter(line => line.trim().length > 0).join('\n');
                    } else {
                        textarea.value = "Sorry, I couldn't generate a summary. The AI response was empty or malformed.";
                    }

                } catch (error) {
                    console.error('Error calling Gemini API:', error);
                    textarea.value = "Sorry, an error occurred while generating the summary. Please try again.";
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = '✨ Generate AI Summary';
                }
            }

            function handlePropertyFormSubmit(e) {
                e.preventDefault();
                const form = e.target;
                const editingId = form.dataset.editingId;
                const formData = new FormData(form);
                const propertyData = Object.fromEntries(formData.entries());
                propertyData.price = parseInt(propertyData.price);
                propertyData.bedrooms = parseInt(propertyData.bedrooms);
                propertyData.bathrooms = parseInt(propertyData.bathrooms);
                propertyData.sqft = parseInt(propertyData.sqft);
                propertyData.imageUrls = [...uploadedImageFiles];
                propertyData.docs = uploadedDocFiles.map(f => f.name);
                propertyData.summary = propertyData.summary.split('\n').filter(s => s.trim() !== '');
                propertyData.ownerId = currentUser.uid;
                propertyData.verificationStatus = 'Pending';
                propertyData.isBoosted = false;
                
                if (editingId) {
                    let prop, index;
                    prop = mockDatabase.pendingProperties.find((p, i) => { if(p.propertyId === editingId) { index = i; return true; }});
                    if (prop) {
                         mockDatabase.pendingProperties[index] = { ...prop, ...propertyData };
                    } else {
                        const allVerified = [...mockDatabase.properties, ...mockDatabase.investorProperties];
                        prop = allVerified.find((p, i) => { if(p.propertyId === editingId) { index = i; return true; }});
                        if(prop) {
                            const propToMove = { ...prop, ...propertyData };
                            if(mockDatabase.investorProperties.some(p => p.propertyId === editingId)) mockDatabase.investorProperties.splice(mockDatabase.investorProperties.findIndex(p => p.propertyId === editingId), 1);
                            else mockDatabase.properties.splice(mockDatabase.properties.findIndex(p => p.propertyId === editingId), 1);
                            mockDatabase.pendingProperties.push(propToMove);
                        }
                    }
                } else {
                    propertyData.propertyId = `prop${Date.now()}`;
                    propertyData.investmentPotentialScore = (7 + Math.random() * 2.5).toFixed(1);
                    propertyData.predictedValueGrowth_1yr = ['medium', 'high'][Math.floor(Math.random() * 2)];
                    propertyData.coords = [34.05 + (Math.random() - 0.5) * 0.1, -118.24 + (Math.random() - 0.5) * 0.1];
                    mockDatabase.pendingProperties.unshift(propertyData);
                }
                uploadedImageFiles = [];
                uploadedDocFiles = [];
                displayUserListings();
                renderPropertyForm();
            }

            function displayUserListings() {
                const container = document.getElementById('user-listings-container');
                if (!container) return;

                const userVerifiedListings = [...mockDatabase.properties, ...mockDatabase.investorProperties].filter(p => p.ownerId === currentUser.uid);
                const userPendingListings = mockDatabase.pendingProperties.filter(p => p.ownerId === currentUser.uid);
                const allUserListings = [...userPendingListings, ...userVerifiedListings];

                if (allUserListings.length === 0) {
                    container.innerHTML = `<div class="bg-white p-6 rounded-2xl border text-center text-slate-500">You haven't added any properties yet.</div>`;
                    return;
                }
                
                if (currentListingView === 'table') {
                    container.innerHTML = `
                        <div class="bg-white p-4 rounded-2xl shadow-lg border overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead class="text-left border-b">
                                    <tr>
                                        <th class="p-4">Address</th>
                                        <th class="p-4">Status</th>
                                        <th class="p-4">Price</th>
                                        <th class="p-4 text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${allUserListings.map(p => createUserListingTableRow(p)).join('')}
                                </tbody>
                            </table>
                        </div>`;
                } else {
                     container.innerHTML = `<div class="space-y-6">${allUserListings.map(p => createUserListingCard(p, true)).join('')}</div>`;
                }

                document.querySelectorAll('.edit-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const property = allUserListings.find(p => p.propertyId === btn.dataset.id);
                        if (property) renderPropertyForm(property);
                    });
                });
                 document.querySelectorAll('.boost-btn').forEach(btn => {
                    btn.addEventListener('click', handleBoostListing);
                });
            }

            // --- ADMIN: PROPERTY VERIFICATION ---
            function renderPropertyVerificationPage() {
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Property Verification</h1><div id="pending-properties-list" class="space-y-6"></div>`;
                displayPendingPropertiesForAdmin();
            }

            function displayPendingPropertiesForAdmin() {
                const container = document.getElementById('pending-properties-list');
                if (mockDatabase.pendingProperties.length === 0) {
                    container.innerHTML = '<div class="bg-white p-8 rounded-2xl text-center text-slate-500 shadow-lg">No properties are currently pending approval.</div>';
                    return;
                }

                container.innerHTML = mockDatabase.pendingProperties.map(p => {
                    const owner = Object.values(mockDatabase.users).find(u => u.uid === p.ownerId);
                    return `
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-200">
                        <div class="grid grid-cols-1 md:grid-cols-3">
                            <div class="md:col-span-1">
                                <img src="${p.imageUrls[0]}" onerror="this.onerror=null;this.src='https://placehold.co/600x400/f8fafc/cbd5e1?text=Image+Not+Found';" class="w-full h-full object-cover">
                            </div>
                            <div class="md:col-span-2 p-6">
                                <p class="font-bold text-xl text-slate-800">${p.address}</p>
                                <p class="text-sm text-slate-500">Submitted by: ${owner ? owner.email : 'Unknown'}</p>
                                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                                    <div><p class="font-semibold">Price</p><p>$${p.price.toLocaleString()}</p></div>
                                    <div><p class="font-semibold">Type</p><p>${p.type}</p></div>
                                    <div><p class="font-semibold">Beds</p><p>${p.bedrooms}</p></div>
                                    <div><p class="font-semibold">Baths</p><p>${p.bathrooms}</p></div>
                                </div>
                                <div class="mt-4">
                                    <p class="font-semibold">Summary</p>
                                    <ul class="list-none space-y-1 mt-1 text-sm">${p.summary.map(s => `<li>${s}</li>`).join('')}</ul>
                                </div>
                                <div class="mt-6 flex gap-4">
                                    <button data-id="${p.propertyId}" class="approve-property-btn w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg">Approve</button>
                                    <button data-id="${p.propertyId}" class="reject-property-btn w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg">Reject</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
                }).join('');

                document.querySelectorAll('.approve-property-btn').forEach(btn => btn.addEventListener('click', handleApproveProperty));
                document.querySelectorAll('.reject-property-btn').forEach(btn => btn.addEventListener('click', handleRejectProperty));
            }

            function handleApproveProperty(e) {
                const propId = e.target.dataset.id;
                const index = mockDatabase.pendingProperties.findIndex(p => p.propertyId === propId);
                if (index > -1) {
                    const propToVerify = mockDatabase.pendingProperties[index];
                    propToVerify.verificationStatus = 'Verified';
                    
                    const owner = Object.values(mockDatabase.users).find(u => u.uid === propToVerify.ownerId);
                    if (owner && owner.userRole === 'investor') {
                         mockDatabase.investorProperties.push(propToVerify);
                    } else {
                        mockDatabase.properties.push(propToVerify);
                    }

                    mockDatabase.pendingProperties.splice(index, 1);
                    displayPendingPropertiesForAdmin();
                }
            }

            function handleRejectProperty(e) {
                const propId = e.target.dataset.id;
                const index = mockDatabase.pendingProperties.findIndex(p => p.propertyId === propId);
                 if (index > -1) {
                    mockDatabase.pendingProperties.splice(index, 1);
                    displayPendingPropertiesForAdmin();
                }
            }

            // --- ADMIN PAGES ---
            function renderFeatureControlPage() {
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Feature Control</h1><div id="feature-flags-container" class="bg-white p-8 rounded-2xl shadow-lg border space-y-4"></div>`;
                const container = document.getElementById('feature-flags-container');
                container.innerHTML = Object.keys(mockDatabase.featureFlags).map(key => {
                    const flag = mockDatabase.featureFlags[key];
                    return `
                        <div class="flex justify-between items-center">
                            <span class="font-semibold">${flag.name}</span>
                            <label class="switch">
                                <input type="checkbox" data-flag="${key}" ${flag.enabled ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    `;
                }).join('');

                document.querySelectorAll('#feature-flags-container input[type="checkbox"]').forEach(toggle => {
                    toggle.addEventListener('change', (e) => {
                        const flagKey = e.target.dataset.flag;
                        mockDatabase.featureFlags[flagKey].enabled = e.target.checked;
                        // Re-build nav for non-admin users if their features change
                        if (currentUser.userRole !== 'admin') {
                           buildNav(currentUser.userRole, currentUser.verificationStatus);
                        }
                    });
                });
            }

            function renderUserManagementPage() {
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">User Management</h1><div class="bg-white p-4 rounded-2xl shadow-lg border"><table class="w-full"><thead><tr class="text-left border-b"><th class="p-4">Email</th><th class="p-4">Role</th><th class="p-4">Status</th><th class="p-4">Actions</th></tr></thead><tbody id="user-list"></tbody></table></div>`;
                const userList = document.getElementById('user-list');
                userList.innerHTML = Object.values(mockDatabase.users).map(user => `
                    <tr class="border-b">
                        <td class="p-4">${user.email}</td>
                        <td class="p-4">${user.userRole}</td>
                        <td class="p-4"><span class="px-2 py-1 text-xs font-semibold rounded-full ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${user.status}</span></td>
                        <td class="p-4">
                            <button data-uid="${user.uid}" class="suspend-btn text-red-600 hover:text-red-800 font-semibold">${user.status === 'Active' ? 'Suspend' : 'Unsuspend'}</button>
                        </td>
                    </tr>
                `).join('');

                document.querySelectorAll('.suspend-btn').forEach(btn => {
                    btn.addEventListener('click', e => {
                        const userToUpdate = Object.values(mockDatabase.users).find(u => u.uid === e.target.dataset.uid);
                        if (userToUpdate) {
                            userToUpdate.status = userToUpdate.status === 'Active' ? 'Suspended' : 'Active';
                            renderUserManagementPage();
                        }
                    });
                });
            }

            function renderAgentVerificationPage() {
                mainContent.innerHTML = `<h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Agent Verification</h1><div id="pending-agents-list" class="space-y-6"></div>`;
                const container = document.getElementById('pending-agents-list');
                const pendingAgents = Object.values(mockDatabase.users).filter(u => u.userRole === 'agent' && u.verificationStatus === 'Pending');

                if (pendingAgents.length === 0) {
                    container.innerHTML = '<div class="bg-white p-8 rounded-2xl text-center text-slate-500 shadow-lg">No agents are currently pending verification.</div>';
                    return;
                }

                container.innerHTML = pendingAgents.map(agent => `
                    <div class="bg-white p-6 rounded-2xl shadow-lg border flex justify-between items-center">
                        <div>
                            <p class="font-bold text-slate-800">${agent.email}</p>
                            <p class="text-sm text-slate-500">Status: ${agent.verificationStatus}</p>
                        </div>
                        <div class="flex gap-4">
                            <button data-uid="${agent.uid}" class="approve-agent-btn bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg">Approve</button>
                            <button data-uid="${agent.uid}" class="reject-agent-btn bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg">Reject</button>
                        </div>
                    </div>
                `).join('');

                document.querySelectorAll('.approve-agent-btn').forEach(btn => {
                    btn.addEventListener('click', e => {
                        const agentToUpdate = Object.values(mockDatabase.users).find(u => u.uid === e.target.dataset.uid);
                        if (agentToUpdate) {
                            agentToUpdate.verificationStatus = 'Verified';
                            renderAgentVerificationPage();
                        }
                    });
                });

                document.querySelectorAll('.reject-agent-btn').forEach(btn => {
                    btn.addEventListener('click', e => {
                        const agentToUpdate = Object.values(mockDatabase.users).find(u => u.uid === e.target.dataset.uid);
                        if (agentToUpdate) {
                           agentToUpdate.verificationStatus = 'Rejected';
                           renderAgentVerificationPage();
                        }
                    });
                });
            }


            // --- AGENT MARKETPLACE & REVIEWS ---
            function renderFindAgentPage() {
                mainContent.innerHTML = `
                    <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Find an Agent</h1>
                    <div class="bg-white rounded-2xl shadow-lg border p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="text-sm font-medium text-slate-700">Specialization</label>
                                <select id="filter-specialization" class="w-full mt-1 p-2 border border-slate-300 rounded-md">
                                    <option value="">All</option>
                                    <option value="Luxury">Luxury</option>
                                    <option value="Waterfront">Waterfront</option>
                                    <option value="Apartments">Apartments</option>
                                    <option value="Villas">Villas</option>
                                </select>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-700">Location</label>
                                <select id="filter-location" class="w-full mt-1 p-2 border border-slate-300 rounded-md">
                                    <option value="">All</option>
                                    <option value="Dubai Marina">Dubai Marina</option>
                                    <option value="Downtown Dubai">Downtown Dubai</option>
                                    <option value="Jumeirah Beach Residence">Jumeirah Beach Residence</option>
                                </select>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-700">Language</label>
                                <select id="filter-language" class="w-full mt-1 p-2 border border-slate-300 rounded-md">
                                    <option value="">All</option>
                                    <option value="English">English</option>
                                    <option value="Arabic">Arabic</option>
                                    <option value="French">French</option>
                                    <option value="Urdu">Urdu</option>
                                </select>
                            </div>
                             <div>
                                <label class="text-sm font-medium text-slate-700">Min Rating</label>
                                <select id="filter-rating" class="w-full mt-1 p-2 border border-slate-300 rounded-md">
                                    <option value="">All</option>
                                    <option value="4.5">4.5+</option>
                                    <option value="4">4.0+</option>
                                    <option value="3">3.0+</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div id="agent-list" class="grid grid-cols-1 md:grid-cols-2 gap-6"></div>
                `;

                displayFilteredAgents();

                document.getElementById('filter-specialization').addEventListener('change', displayFilteredAgents);
                document.getElementById('filter-location').addEventListener('change', displayFilteredAgents);
                document.getElementById('filter-language').addEventListener('change', displayFilteredAgents);
                document.getElementById('filter-rating').addEventListener('change', displayFilteredAgents);
            }

            function displayFilteredAgents() {
                const specialization = document.getElementById('filter-specialization').value;
                const location = document.getElementById('filter-location').value;
                const language = document.getElementById('filter-language').value;
                const minRating = parseFloat(document.getElementById('filter-rating').value) || 0;

                let agents = Object.values(mockDatabase.users).filter(u => u.userRole === 'agent' && u.verificationStatus === 'Verified');

                if (specialization) {
                    agents = agents.filter(a => a.specialization.includes(specialization));
                }
                if (location) {
                    agents = agents.filter(a => a.location === location);
                }
                if (language) {
                    agents = agents.filter(a => a.languages.includes(language));
                }
                if (minRating) {
                    agents = agents.filter(a => a.rating >= minRating);
                }

                const agentListContainer = document.getElementById('agent-list');
                agentListContainer.innerHTML = '';

                if (agents.length > 0) {
                    agentListContainer.innerHTML = agents.map(agent => createAgentListRow(agent)).join('');
                } else {
                    agentListContainer.innerHTML = `<p class="text-center text-slate-500 col-span-full">No agents match your criteria.</p>`;
                }
                
                document.querySelectorAll('.view-agent-profile-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => navigateTo('agent_profile', e.target.dataset.id));
                });
            }
            
            function createAgentListRow(agent) {
                return `
                <div class="bg-white rounded-2xl p-4 card-shadow border border-slate-200 flex items-center gap-4">
                    <img src="${agent.imageUrl}" class="w-20 h-20 rounded-full object-cover flex-shrink-0">
                    <div class="flex-grow">
                        <p class="font-bold text-lg text-slate-800">${agent.name}</p>
                        <p class="text-sm text-slate-500">${agent.location}</p>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="text-amber-500 font-bold flex items-center">★ ${agent.rating}</span>
                            <span class="text-xs text-slate-400">(${agent.reviews || 0} reviews)</span>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                         <button data-id="${agent.uid}" class="view-agent-profile-btn w-full btn-primary font-semibold py-2 px-4 rounded-lg text-sm">View Profile</button>
                    </div>
                </div>
                `;
            }

            function renderAgentProfilePage(agentId) {
                const agent = Object.values(mockDatabase.users).find(u => u.uid === agentId);
                if (!agent) {
                    mainContent.innerHTML = `<p>Agent not found.</p>`;
                    return;
                }

                const agentListings = [...mockDatabase.properties, ...mockDatabase.investorProperties].filter(p => p.ownerId === agent.uid && p.verificationStatus === 'Verified');
                let listingsHtml = `<p class="text-center text-slate-500 py-8 col-span-full">This agent has no active listings.</p>`;
                if(agentListings.length > 0) {
                    listingsHtml = `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">${agentListings.map(p => createPropertyCard(p, false)).join('')}</div>`;
                }

                const agentReviews = mockDatabase.reviews.filter(r => r.agentId === agent.uid);
                const avgRating = agentReviews.length > 0 ? (agentReviews.reduce((acc, r) => acc + r.rating, 0) / agentReviews.length).toFixed(1) : 'No reviews';
                let reviewsHtml = `<div class="bg-white rounded-2xl p-8 text-center text-slate-500"><p>This agent has not received any reviews yet.</p></div>`;
                if(agentReviews.length > 0) {
                    reviewsHtml = agentReviews.map(review => {
                        const reviewer = Object.values(mockDatabase.users).find(u => u.uid === review.userId);
                        return `
                        <div class="bg-white p-6 rounded-2xl border border-slate-200">
                            <div class="flex items-center mb-2">
                                <div class="flex text-amber-400">${'★'.repeat(review.rating)}${'☆'.repeat(5 - review.rating)}</div>
                                <p class="ml-auto text-sm text-slate-500">${new Date(review.date).toLocaleDateString()}</p>
                            </div>
                            <p class="text-slate-600">"${review.text}"</p>
                            <p class="text-right text-sm font-semibold text-slate-700 mt-2">- ${reviewer ? reviewer.email.split('@')[0] : 'A user'}</p>
                        </div>
                        `;
                    }).join('');
                }

                mainContent.innerHTML = `
                    <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 flex flex-col md:flex-row items-center gap-8">
                        <img src="${agent.imageUrl}" class="w-32 h-32 rounded-full object-cover flex-shrink-0">
                        <div class="text-center md:text-left">
                            <h1 class="text-4xl font-extrabold text-slate-900">${agent.name}</h1>
                            <p class="text-lg text-green-600 font-semibold flex items-center justify-center md:justify-start gap-2 mt-1">✔️ Verified Agent</p>
                            <div class="flex items-center justify-center md:justify-start gap-2 mt-2">
                                <span class="text-amber-500 font-bold text-lg">★ ${avgRating}</span>
                                <span class="text-slate-600">(${agentReviews.length} reviews)</span>
                            </div>
                        </div>
                        <div class="ml-auto flex-shrink-0 space-y-3">
                             <button id="contact-agent-btn" data-agent-id="${agent.uid}" class="w-full btn-primary font-semibold py-3 px-8 rounded-lg text-lg">Contact Agent</button>
                             <button id="review-agent-btn" data-agent-id="${agent.uid}" class="w-full bg-slate-200 text-slate-800 font-semibold py-3 px-8 rounded-lg text-lg hover:bg-slate-300">Leave a Review</button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h2 class="text-3xl font-bold text-slate-900 mb-6">Active Listings</h2>
                            <div class="space-y-6">${listingsHtml}</div>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold text-slate-900 mb-6">Client Reviews</h2>
                            <div class="space-y-4">${reviewsHtml}</div>
                        </div>
                    </div>
                `;
                document.getElementById('contact-agent-btn').addEventListener('click', handleContactAgent);
                document.getElementById('review-agent-btn').addEventListener('click', handleReviewAgent);
            }

            function handleContactAgent(e) {
                const agentId = e.target.dataset.agentId;
                const agent = mockDatabase.users[Object.keys(mockDatabase.users).find(key => mockDatabase.users[key].uid === agentId)];
                
                const modalContent = `
                    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg fade-in">
                        <div class="p-6 border-b">
                            <h3 class="text-2xl font-bold">Contact ${agent.email}</h3>
                        </div>
                        <form id="contact-agent-form" class="p-6 space-y-4">
                            <input type="hidden" name="agentId" value="${agentId}">
                            <div>
                                <label for="message" class="text-sm font-medium text-slate-700">Your Message</label>
                                <textarea id="message" name="message" rows="5" required class="mt-1 block w-full p-3 border border-slate-300 rounded-md" placeholder="Hi, I'm interested in your services..."></textarea>
                            </div>
                            <div class="flex justify-end gap-4">
                                <button type="button" id="cancel-contact-btn" class="bg-slate-200 text-slate-800 font-semibold py-2 px-6 rounded-lg">Cancel</button>
                                <button type="submit" class="btn-primary font-semibold py-2 px-6 rounded-lg">Send Message</button>
                            </div>
                        </form>
                    </div>
                `;
                showModal(null, null, modalContent);

                document.getElementById('cancel-contact-btn').addEventListener('click', hideModal);
                document.getElementById('contact-agent-form').addEventListener('submit', e => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const leadData = Object.fromEntries(formData.entries());
                    
                    mockDatabase.leads.unshift({
                        leadId: `lead${Date.now()}`,
                        agentId: leadData.agentId,
                        userId: currentUser.uid,
                        userEmail: currentUser.email,
                        message: leadData.message,
                        status: 'new',
                        date: new Date().toISOString()
                    });

                    hideModal();
                    setTimeout(() => showModal("Success!", "Your message has been sent to the agent."), 300);
                });
            }

            function handleReviewAgent(e) {
                const agentId = e.target.dataset.agentId;
                const modalContent = `
                    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg fade-in">
                        <div class="p-6 border-b">
                            <h3 class="text-2xl font-bold">Leave a Review</h3>
                        </div>
                        <form id="review-agent-form" class="p-6 space-y-4">
                            <input type="hidden" name="agentId" value="${agentId}">
                            <div>
                                <label class="text-sm font-medium text-slate-700">Your Rating</label>
                                <div class="star-rating">
                                    <input type="radio" id="5-stars" name="rating" value="5" /><label for="5-stars">★</label>
                                    <input type="radio" id="4-stars" name="rating" value="4" /><label for="4-stars">★</label>
                                    <input type="radio" id="3-stars" name="rating" value="3" /><label for="3-stars">★</label>
                                    <input type="radio" id="2-stars" name="rating" value="2" /><label for="2-stars">★</label>
                                    <input type="radio" id="1-star" name="rating" value="1" required /><label for="1-star">★</label>
                                </div>
                            </div>
                            <div>
                                <label for="review-text" class="text-sm font-medium text-slate-700">Your Review</label>
                                <textarea id="review-text" name="text" rows="5" required class="mt-1 block w-full p-3 border border-slate-300 rounded-md" placeholder="Share your experience with this agent..."></textarea>
                            </div>
                            <div class="flex justify-end gap-4">
                                <button type="button" id="cancel-review-btn" class="bg-slate-200 text-slate-800 font-semibold py-2 px-6 rounded-lg">Cancel</button>
                                <button type="submit" class="btn-primary font-semibold py-2 px-6 rounded-lg">Submit Review</button>
                            </div>
                        </form>
                    </div>
                `;
                showModal(null, null, modalContent);
                document.getElementById('cancel-review-btn').addEventListener('click', hideModal);
                document.getElementById('review-agent-form').addEventListener('submit', e => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const reviewData = Object.fromEntries(formData.entries());
                    mockDatabase.reviews.unshift({
                        reviewId: `rev${Date.now()}`,
                        agentId: reviewData.agentId,
                        userId: currentUser.uid,
                        rating: parseInt(reviewData.rating),
                        text: reviewData.text,
                        date: new Date().toISOString()
                    });
                    hideModal();
                    setTimeout(() => {
                        showModal("Thank You!", "Your review has been submitted.");
                        renderAgentProfilePage(reviewData.agentId); // Re-render to show the new review
                    }, 300);
                });
            }

            // --- AGENT LEADS PAGE ---
            function renderAgentLeadsPage() {
                const agent = mockDatabase.users[currentUser.email];
                mainContent.innerHTML = `
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900">My Leads</h1>
                        <div class="bg-white p-3 rounded-lg shadow border">
                            <span class="font-bold text-lg text-orange-600">${agent.credits || 0}</span>
                            <span class="text-slate-600">Credits</span>
                        </div>
                    </div>
                    <div id="leads-container" class="space-y-4"></div>
                `;
                displayAgentLeads();
            }

            function displayAgentLeads() {
                const container = document.getElementById('leads-container');
                const agentLeads = mockDatabase.leads.filter(lead => lead.agentId === currentUser.uid);

                if (agentLeads.length === 0) {
                    container.innerHTML = `<div class="bg-white p-8 rounded-2xl text-center text-slate-500 shadow-lg">You have no new leads.</div>`;
                    return;
                }

                container.innerHTML = agentLeads.map(lead => {
                    const agent = mockDatabase.users[currentUser.email];
                    const canAccept = (agent.credits || 0) > 0;
                    let actionButtons = '';

                    if (lead.status === 'new') {
                        actionButtons = `
                        <div class="flex gap-3 mt-4 md:mt-0">
                            <button data-lead-id="${lead.leadId}" ${!canAccept ? 'disabled' : ''} class="accept-lead-btn w-full md:w-auto bg-green-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-600 disabled:bg-slate-300 disabled:cursor-not-allowed">${canAccept ? 'Accept (1 Credit)' : 'No Credits'}</button>
                            <button data-lead-id="${lead.leadId}" class="reject-lead-btn w-full md:w-auto bg-red-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-red-600">Reject</button>
                        </div>`;
                    } else if (lead.status === 'accepted') {
                        actionButtons = `<div class="text-green-600 font-bold text-right">Accepted<br><span class="font-normal">Contact: ${lead.userEmail}</span></div>`;
                    } else { // rejected
                        actionButtons = `<div class="text-red-500 font-bold text-right">Rejected</div>`;
                    }

                    return `
                    <div class="bg-white p-6 rounded-2xl shadow-lg border flex flex-col md:flex-row justify-between items-start">
                        <div>
                            <p class="text-sm text-slate-500">From: <span class="font-semibold text-slate-700">${lead.status === 'accepted' ? lead.userEmail : 'Hidden until accepted'}</span></p>
                            <p class="mt-2 text-slate-800 bg-slate-50 p-3 rounded-md">${lead.message}</p>
                        </div>
                        ${actionButtons}
                    </div>
                    `;
                }).join('');

                document.querySelectorAll('.accept-lead-btn').forEach(btn => btn.addEventListener('click', handleAcceptLead));
                document.querySelectorAll('.reject-lead-btn').forEach(btn => btn.addEventListener('click', handleRejectLead));
            }

            function handleAcceptLead(e) {
                const leadId = e.target.dataset.leadId;
                const lead = mockDatabase.leads.find(l => l.leadId === leadId);
                const agent = mockDatabase.users[currentUser.email];
                if (lead && agent && (agent.credits || 0) > 0) {
                    agent.credits -= 1;
                    lead.status = 'accepted';
                    renderAgentLeadsPage(); // Re-render the whole page to update credits and lead status
                }
            }
            function handleRejectLead(e) {
                const leadId = e.target.dataset.leadId;
                const lead = mockDatabase.leads.find(l => l.leadId === leadId);
                if (lead) {
                    lead.status = 'rejected';
                    displayAgentLeads();
                }
            }
            
            // --- PROPERTY FINDER PAGE (CONVERSATIONAL CANVAS) ---
            function renderPropertyFinderPage() {
                mainContent.innerHTML = `
                <div class="flex flex-col h-full w-full bg-white overflow-hidden">
                    <div id="chat-messages" class="flex-grow p-4 space-y-4 overflow-y-auto chat-messages">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center text-sm font-bold">AI</div>
                            <div class="bg-slate-100 p-3 rounded-lg rounded-tl-none">
                                <p class="text-sm">Hello! I'm your AI Property Finder. How can I help you find a property today?</p>
                            </div>
                        </div>
                    </div>
                    <div id="contextual-bar" class="hidden p-2 bg-amber-100 border-t border-b border-amber-200 text-amber-800 text-sm flex justify-between items-center">
                        <span id="contextual-text"></span>
                        <button id="end-discussion-btn" class="font-bold">×</button>
                    </div>
                    <div class="flex-shrink-0 p-4 border-t bg-slate-50">
                        <div class="flex items-center gap-2 mb-2">
                            <button id="filter-btn" class="bg-slate-200 text-slate-700 text-sm font-semibold py-1 px-3 rounded-full hover:bg-slate-300">Filters</button>
                            <button id="map-view-btn" class="hidden bg-slate-200 text-slate-700 text-sm font-semibold py-1 px-3 rounded-full hover:bg-slate-300">View on Map</button>
                        </div>
                        <form id="ai-chat-form" class="flex items-center gap-3">
                            <input type="text" id="chat-input" placeholder="Type your message..." class="flex-grow p-3 border border-slate-300 rounded-lg focus:ring-orange-500 focus:border-orange-500" required>
                            <button type="submit" class="btn-primary p-3 rounded-lg flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" /></svg>
                            </button>
                        </form>
                    </div>
                </div>
                `;
                
                initializeFinder();
            }

            function initializeFinder() {
                document.getElementById('ai-chat-form').addEventListener('submit', handleAIChatSubmit);
                document.getElementById('filter-btn').addEventListener('click', renderFiltersModal);
                document.getElementById('map-view-btn').addEventListener('click', renderMapModal);
                document.getElementById('end-discussion-btn').addEventListener('click', endContextualDiscussion);

                // Delegated event listener for discuss buttons
                document.getElementById('chat-messages').addEventListener('click', function(e) {
                    if (e.target.closest('.discuss-btn')) {
                        const button = e.target.closest('.discuss-btn');
                        const propId = button.dataset.id;
                        startContextualDiscussion(propId);
                    }
                });
            }
            
            function initializeMap(mapContainerId) {
                if (document.getElementById(mapContainerId) && !mapInstance) {
                    mapInstance = L.map(mapContainerId).setView([40, -95], 4);
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        maxZoom: 19,
                        attribution: '© OpenStreetMap'
                    }).addTo(mapInstance);
                }
            }

            function startContextualDiscussion(propId) {
                contextualProperty = [...mockDatabase.properties, ...mockDatabase.investorProperties, ...mockDatabase.pendingProperties].find(p => p.propertyId === propId);
                if (contextualProperty) {
                    const bar = document.getElementById('contextual-bar');
                    const text = document.getElementById('contextual-text');
                    text.textContent = `Discussing: ${contextualProperty.address}`;
                    bar.classList.remove('hidden');
                    document.getElementById('chat-input').focus();
                }
            }

            function endContextualDiscussion() {
                contextualProperty = null;
                document.getElementById('contextual-bar').classList.add('hidden');
            }

            function renderFiltersModal() {
                 const modalContent = `
                    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md fade-in">
                        <div class="p-6 border-b">
                            <h3 class="text-2xl font-bold">Filters</h3>
                        </div>
                        <form id="filter-form" class="p-6 space-y-4">
                            <div>
                                <label class="text-sm font-medium text-slate-700">Property Type</label>
                                <select name="type" class="w-full mt-1 p-3 border border-slate-300 rounded-md">
                                    <option value="">Any</option>
                                    <option value="House">House</option>
                                    <option value="Condo">Condo</option>
                                    <option value="Townhouse">Townhouse</option>
                                    <option value="Commercial">Commercial</option>
                                </select>
                            </div>
                             <div>
                                <label class="text-sm font-medium text-slate-700">Min Bedrooms</label>
                                <input name="minBeds" type="number" min="0" class="w-full mt-1 p-3 border border-slate-300 rounded-md" placeholder="Any">
                            </div>
                             <div>
                                <label class="text-sm font-medium text-slate-700">Max Price</label>
                                <input name="maxPrice" type="number" min="0" class="w-full mt-1 p-3 border border-slate-300 rounded-md" placeholder="Any">
                            </div>
                            <div class="flex justify-end gap-4 pt-4">
                                <button type="button" id="cancel-filter-btn" class="bg-slate-200 text-slate-800 font-semibold py-2 px-6 rounded-lg">Cancel</button>
                                <button type="submit" class="btn-primary font-semibold py-2 px-6 rounded-lg">Apply Filters</button>
                            </div>
                        </form>
                    </div>
                `;
                showModal(null, null, modalContent);
                document.getElementById('cancel-filter-btn').addEventListener('click', hideModal);
                document.getElementById('filter-form').addEventListener('submit', e => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const filters = Object.fromEntries(formData.entries());
                    hideModal();
                    handleSearch(null, filters);
                });
            }

            function renderMapModal() {
                const modalContent = `
                    <div class="bg-white rounded-2xl shadow-2xl w-full h-full max-w-4xl max-h-[90vh] fade-in flex flex-col">
                        <div class="p-4 border-b flex justify-between items-center flex-shrink-0">
                            <h3 class="text-2xl font-bold">Map View</h3>
                             <button id="close-map-btn" class="text-slate-500 hover:text-slate-800">×</button>
                        </div>
                        <div id="map-view-modal" class="w-full h-full flex-grow"></div>
                    </div>
                `;
                 showModal(null, null, modalContent);
                 document.getElementById('close-map-btn').addEventListener('click', hideModal);
                 
                 setTimeout(() => {
                    initializeMap('map-view-modal');
                    updateMapMarkers(currentFilteredProperties);
                 }, 100); // Small delay to ensure modal is rendered
            }


            function handleAIChatSubmit(e) {
                e.preventDefault();
                const input = document.getElementById('chat-input');
                const message = input.value.trim();
                if (!message) return;
                handleSearch(message);
            }

            function handleSearch(message, filters = {}) {
                if (message) {
                    addChatMessage(message, 'user');
                    document.getElementById('chat-input').value = '';
                }
                addChatMessage(null, 'ai', { isTyping: true });

                setTimeout(() => {
                    let allProps = [...mockDatabase.properties, ...mockDatabase.investorProperties].filter(p => p.verificationStatus === 'Verified');
                    let filtered = allProps;
                    
                    let query = message ? message.toLowerCase() : '';
                    if (contextualProperty) {
                        query = `Regarding ${contextualProperty.address}, ${query}`;
                    }

                    // Apply filters from modal
                    if (filters.type) filtered = filtered.filter(p => p.type === filters.type);
                    if (filters.minBeds) filtered = filtered.filter(p => p.bedrooms >= parseInt(filters.minBeds));
                    if (filters.maxPrice) filtered = filtered.filter(p => p.price <= parseInt(filters.maxPrice));

                    // Apply filters from chat query
                    if (query.includes('house')) filtered = filtered.filter(p => p.type.toLowerCase() === 'house');
                    if (query.includes('condo')) filtered = filtered.filter(p => p.type.toLowerCase() === 'condo');
                    const bedMatch = query.match(/(\d+)\s*bed/);
                    if (bedMatch) filtered = filtered.filter(p => p.bedrooms >= parseInt(bedMatch[1]));
                    const priceMatch = query.match(/under\s*\$?([\d,]+)/);
                    if (priceMatch) filtered = filtered.filter(p => p.price <= parseInt(priceMatch[1].replace(/,/g, '')));
                    
                    filtered.sort((a, b) => (b.isBoosted ? 1 : 0) - (a.isBoosted ? 1 : 0));
                    currentFilteredProperties = filtered;
                    updatePropertyResults(filtered, null);

                }, 1500);
            }
            
            function addChatMessage(message, sender, options = {}) {
                const { isTyping = false, contentHtml = '' } = options;
                const chatMessages = document.getElementById('chat-messages');
                
                const existingTyping = document.getElementById('typing-indicator');
                if (existingTyping) {
                    existingTyping.remove();
                }

                const messageElement = document.createElement('div');
                
                if (isTyping) {
                    messageElement.id = 'typing-indicator';
                    messageElement.className = 'flex items-start gap-3';
                    messageElement.innerHTML = `
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center text-sm font-bold">AI</div>
                        <div class="bg-slate-100 p-3 rounded-lg rounded-tl-none">
                            <p class="text-sm font-medium animate-pulse">Thinking...</p>
                        </div>`;
                } else if (sender === 'user') {
                    messageElement.className = 'flex items-start gap-3 justify-end';
                    messageElement.innerHTML = `
                        <div class="bg-orange-500 text-white p-3 rounded-lg rounded-br-none max-w-xs">
                            <p class="text-sm break-words">${message}</p>
                        </div>
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-slate-200 text-slate-600 flex items-center justify-center text-sm font-bold">${currentUser.email.charAt(0).toUpperCase()}</div>`;
                } else { // sender === 'ai'
                    messageElement.className = 'flex items-start gap-3';
                    messageElement.innerHTML = `
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center text-sm font-bold">AI</div>
                        <div class="bg-slate-100 p-3 rounded-lg rounded-tl-none max-w-full">
                            ${message ? `<p class="text-sm break-words mb-2">${message}</p>` : ''}
                            ${contentHtml}
                        </div>`;
                }
                chatMessages.appendChild(messageElement);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function updatePropertyResults(properties, aiResponseText = null) {
                const responseText = aiResponseText !== null ? aiResponseText : (properties.length > 0 ? `I found ${properties.length} properties matching your criteria.` : `I couldn't find any properties matching your criteria. Please try a different search.`);
                
                const cardsHtml = `<div class="space-y-4 py-2">${properties.map(p => createPropertyCard(p, false)).join('')}</div>`;
                
                addChatMessage(responseText, 'ai', { contentHtml: properties.length > 0 ? cardsHtml : '' });

                const mapViewBtn = document.getElementById('map-view-btn');
                if (properties.length > 0) {
                    mapViewBtn.classList.remove('hidden');
                } else {
                    mapViewBtn.classList.add('hidden');
                }
            }
            
            function updateMapMarkers(properties) {
                 if (!mapInstance) return;
                 mapMarkers.forEach(marker => marker.remove());
                 mapMarkers = [];
                 if (properties.length > 0) {
                     properties.forEach(p => {
                         if (p.coords) {
                             const marker = L.marker(p.coords).addTo(mapInstance);
                             marker.bindPopup(`<b>${p.address}</b><br>$${p.price.toLocaleString()}`);
                             mapMarkers.push(marker);
                         }
                     });
                     const group = new L.featureGroup(mapMarkers);
                     mapInstance.fitBounds(group.getBounds().pad(0.2));
                 } else {
                     mapInstance.setView([40, -95], 4);
                 }
            }


            // --- MODAL & COMPONENT BUILDERS ---
            function showModal(title, message, content = '') {
                if(content) {
                    modalContainer.innerHTML = content;
                } else {
                     modalContainer.innerHTML = `
                        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-sm text-center p-6 fade-in">
                            <h3 class="text-2xl font-bold mb-2">${title}</h3>
                            <p class="text-slate-600 mb-6">${message}</p>
                            <button id="modal-close-btn" class="btn-primary font-semibold py-2 px-8 rounded-lg">OK</button>
                        </div>
                    `;
                }
                modalContainer.classList.remove('hidden');
                modalContainer.classList.add('flex');
                const closeButton = document.getElementById('modal-close-btn');
                if(closeButton) closeButton.addEventListener('click', hideModal);
            }

            function hideModal() {
                modalContainer.classList.add('hidden');
                modalContainer.classList.remove('flex');
                modalContainer.innerHTML = '';
            }

            function createCard(t, c) { return `<div class="bg-white p-6 rounded-2xl card-shadow border border-slate-200"><h3 class="text-xl font-bold text-slate-900 mb-4">${t}</h3><div>${c}</div></div>`; }
            function createRecommendedComponent(user) {
                const allVerifiedProps = [...mockDatabase.properties, ...mockDatabase.investorProperties].filter(p => p.verificationStatus === 'Verified');
                const viewedTypes = user.recentlyViewed.map(id => (allVerifiedProps.find(p => p.propertyId === id))?.type).filter(Boolean);
                const recommendations = allVerifiedProps.filter(p => !user.recentlyViewed.includes(p.propertyId) && (viewedTypes.length === 0 || viewedTypes.includes(p.type))).slice(0, 3);
                if (recommendations.length === 0) return createCard('Recommended For You', '<p class="text-slate-500">View some properties to get personalized recommendations.</p>');
                return createCard('Recommended For You', `<div class="space-y-4">${recommendations.map(prop => `<div class="flex items-center gap-4"><img src="${prop.imageUrls[0]}" onerror="this.onerror=null;this.src='https://placehold.co/80x80/f8fafc/cbd5e1?text=Image';" class="w-20 h-20 rounded-lg object-cover"><div class="flex-grow"><p class="font-semibold">${prop.address}</p><p class="text-sm text-slate-500">${prop.bedrooms} bed, ${prop.bathrooms} bath</p><p class="font-bold text-orange-600 mt-1">$${prop.price.toLocaleString()}</p></div></div>`).join('')}</div>`);
            }
            function createRecentlyViewedComponent(user) {
                if (user.recentlyViewed.length === 0) return createCard('Recently Viewed', '<p class="text-slate-500">Your recently viewed properties will appear here.</p>');
                const allProps = [...mockDatabase.properties, ...mockDatabase.investorProperties, ...mockDatabase.pendingProperties];
                const properties = user.recentlyViewed.map(id => allProps.find(p => p.propertyId === id)).filter(Boolean).reverse();
                return createCard('Recently Viewed', `<div class="space-y-4">${properties.map(prop => `<div class="flex items-center gap-4"><img src="${prop.imageUrls[0]}" onerror="this.onerror=null;this.src='https://placehold.co/64x64/f8fafc/cbd5e1?text=Image';" class="w-16 h-16 rounded-lg object-cover"><div class="flex-grow"><p class="font-semibold text-sm">${prop.address}</p><p class="text-xs text-slate-500">${prop.type}</p></div></div>`).join('')}</div>`);
            }
            
            function getAmenityIcon(amenity) {
                const icons = {
                    "Swimming Pool": '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h10a2 2 0 002-2v-1a2 2 0 012-2h1.945M7.884 6.884L10 9m4-2.116L12 9m-7 11h14" /></svg>',
                    "Gym": '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>', // Placeholder, using a lightning bolt
                    "Covered Parking": '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>', // Placeholder, using a checkmark
                    "24/7 Security": '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.286zm-1.5 6.445l-1.5 1.5M15 15l-1.5-1.5" /></svg>'
                };
                return icons[amenity] || '';
            }

            function createPropertyCard(p, isEditable = false) {
                const priceLabel = p.status === 'rent' ? '/month' : '';
                const imageUrl = p.imageUrls && p.imageUrls.length > 0 ? p.imageUrls[0] : 'https://placehold.co/600x400/f8fafc/cbd5e1?text=Image+Not+Found';
                
                let actionButton;
                if (isEditable) {
                    actionButton = `<button data-id="${p.propertyId}" class="edit-btn font-semibold py-2 px-4 rounded-lg bg-slate-200 hover:bg-slate-300 text-slate-800 text-sm">Edit</button>`;
                } else {
                    actionButton = `<button data-id="${p.propertyId}" class="discuss-btn btn-primary py-2 px-4 rounded-lg text-sm flex items-center gap-1">✨ Discuss</button>`;
                }
                
                const amenitiesHtml = p.amenities && p.amenities.length > 0 ? `
                    <div class="mt-2 pt-2 border-t border-slate-200">
                        <div class="flex flex-wrap gap-2 text-xs">
                            ${p.amenities.map(amenity => `
                                <span class="flex items-center gap-1.5 bg-slate-100 px-2 py-1 rounded-full text-slate-600">
                                    ${getAmenityIcon(amenity)}
                                    ${amenity}
                                </span>
                            `).join('')}
                        </div>
                    </div>
                ` : '';
                
                const scoreHtml = (score, label) => {
                    const color = score === 'High' ? 'green' : score === 'Medium' ? 'orange' : 'red';
                    return `<div class="flex items-center gap-1"><span class="h-2 w-2 rounded-full bg-${color}-500"></span><span class="text-xs font-semibold text-slate-600">${label}: ${score}</span></div>`;
                };

                return `
                <div class="property-card bg-white rounded-2xl card-shadow border border-slate-200 overflow-hidden fade-in" data-id="${p.propertyId}">
                    <div class="relative">
                        <img src="${imageUrl}" onerror="this.onerror=null;this.src='https://placehold.co/600x400/f8fafc/cbd5e1?text=Image+Not+Found';" alt="Property image for ${p.address}" class="w-full h-40 object-cover">
                    </div>
                    <div class="p-4 flex flex-col">
                        <div class="flex justify-between items-start">
                            <p class="font-bold text-md text-slate-800 truncate">${p.address}</p>
                            <p class="text-xl font-extrabold text-slate-900">$${p.price.toLocaleString()}<span class="text-sm font-medium text-slate-500">${priceLabel}</span></p>
                        </div>
                        <p class="text-sm text-slate-500">${p.bedrooms} beds • ${p.bathrooms} baths • ${p.sqft} sqft</p>
                        <div class="flex gap-4 mt-2">
                             ${scoreHtml(p.valueForMoneyScore, 'Value')}
                             ${scoreHtml(p.demandScore, 'Demand')}
                        </div>
                        ${amenitiesHtml}
                        <div class="flex-grow mt-4 flex justify-end items-center">
                            ${actionButton}
                        </div>
                    </div>
                </div>`;
            }

            function logRecentView(propertyId) {
                if (!currentUser || !currentUser.recentlyViewed) return;
                const index = currentUser.recentlyViewed.indexOf(propertyId);
                if (index > -1) currentUser.recentlyViewed.splice(index, 1);
                currentUser.recentlyViewed.push(propertyId);
                if (currentUser.recentlyViewed.length > 3) currentUser.recentlyViewed.shift();
                
                if(document.querySelector('.nav-item.active')?.dataset.page === 'dashboard') {
                    renderDashboardPage(currentUser);
                }
            }
            
            // --- NEW & IMPROVED DASHBOARDS ---

            function renderAgentDashboardPage() {
                const agentId = currentUser.uid;
                const agentListings = [...mockDatabase.properties, ...mockDatabase.investorProperties].filter(p => p.ownerId === agentId && p.verificationStatus === 'Verified');
                const agentLeads = mockDatabase.leads.filter(l => l.agentId === agentId);
                const agentReviews = mockDatabase.reviews.filter(r => r.agentId === agentId);

                const kpis = {
                    activeListings: agentListings.length,
                    newLeadsThisMonth: agentLeads.filter(l => new Date(l.date).getMonth() === new Date().getMonth() && new Date(l.date).getFullYear() === new Date().getFullYear()).length,
                    propertiesSold: agentListings.filter(p => p.soldDate).length,
                    avgRating: agentReviews.length > 0 ? (agentReviews.reduce((acc, r) => acc + r.rating, 0) / agentReviews.length).toFixed(1) : 'N/A'
                };

                mainContent.innerHTML = `
                    <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Agent Dashboard</h1>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Active Listings</p><p class="text-4xl font-bold">${kpis.activeListings}</p></div>
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">New Leads (This Month)</p><p class="text-4xl font-bold">${kpis.newLeadsThisMonth}</p></div>
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Properties Sold (YTD)</p><p class="text-4xl font-bold">${kpis.propertiesSold}</p></div>
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Average Rating</p><p class="text-4xl font-bold text-amber-500">★ ${kpis.avgRating}</p></div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
                        <div class="lg:col-span-3 bg-white p-6 rounded-2xl shadow-lg border"><h3 class="font-bold text-lg mb-4">Listings Performance (Last 30 Days)</h3><div class="h-80"><canvas id="listings-performance-chart"></canvas></div></div>
                        <div class="lg:col-span-2 bg-white p-6 rounded-2xl shadow-lg border"><h3 class="font-bold text-lg mb-4">Leads by Status</h3><div class="h-80"><canvas id="leads-status-chart"></canvas></div></div>
                    </div>
                `;

                // Render Charts
                const leadsByStatus = agentLeads.reduce((acc, lead) => {
                    acc[lead.status] = (acc[lead.status] || 0) + 1;
                    return acc;
                }, {});
                
                renderChart(document.getElementById('leads-status-chart'), 'doughnut', {
                    labels: Object.keys(leadsByStatus),
                    datasets: [{
                        label: 'Leads',
                        data: Object.values(leadsByStatus),
                        backgroundColor: ['#f97316', '#22c55e', '#ef4444'],
                    }]
                });

                renderChart(document.getElementById('listings-performance-chart'), 'line', {
                    labels: agentListings.map(p => p.address.substring(0, 15) + '...'),
                    datasets: [
                        { label: 'Views', data: agentListings.map(p => p.views), borderColor: '#3b82f6', backgroundColor: '#3b82f620', tension: 0.1, fill: true },
                        { label: 'Inquiries', data: agentListings.map(p => p.inquiries), borderColor: '#16a34a', backgroundColor: '#16a34a20', tension: 0.1, fill: true }
                    ]
                });
            }

            function renderInvestorDashboardPage() {
                const investorId = currentUser.uid;
                const portfolio = [...mockDatabase.investorProperties, ...mockDatabase.properties].filter(p => p.ownerId === investorId);
                const pendingCount = mockDatabase.pendingProperties.filter(p => p.ownerId === investorId).length;
                const portfolioValue = portfolio.reduce((sum, p) => sum + p.price, 0);

                mainContent.innerHTML = `
                    <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Investor Dashboard</h1>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Total Portfolio Value</p><p class="text-4xl font-bold">$${portfolioValue.toLocaleString()}</p></div>
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Properties in Portfolio</p><p class="text-4xl font-bold">${portfolio.length}</p></div>
                        <div class="bg-white p-6 rounded-2xl shadow-lg border"><p class="text-sm text-slate-500">Pending Verifications</p><p class="text-4xl font-bold">${pendingCount}</p></div>
                    </div>
                    <div class="bg-white p-6 rounded-2xl shadow-lg border"><h3 class="font-bold text-lg mb-4">Portfolio Value Over Time</h3><div class="h-96"><canvas id="portfolio-value-chart"></canvas></div></div>
                `;

                // Create dummy historical data for the chart
                const history = [
                    { month: 'Jan', amount: portfolioValue * 0.85 },
                    { month: 'Feb', amount: portfolioValue * 0.88 },
                    { month: 'Mar', amount: portfolioValue * 0.90 },
                    { month: 'Apr', amount: portfolioValue * 0.92 },
                    { month: 'May', amount: portfolioValue * 0.95 },
                    { month: 'Jun', amount: portfolioValue * 0.98 },
                    { month: 'Jul', amount: portfolioValue }
                ];

                renderChart(document.getElementById('portfolio-value-chart'), 'line', {
                    labels: history.map(d => d.month),
                    datasets: [{
                        label: 'Portfolio Value',
                        data: history.map(d => d.amount),
                        borderColor: 'var(--brand-orange)',
                        backgroundColor: 'rgba(221, 107, 32, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                });
            }

            function createUserListingCard(p, isEditable = false) {
                 const canBoost = p.verificationStatus === 'Verified' && !p.isBoosted;
                const boostCost = 5; // Example cost
                const user = mockDatabase.users[currentUser.email];
                const hasEnoughCredits = (user.credits || 0) >= boostCost;

                let actionButtonHtml = '';
                if (canBoost) {
                    actionButtonHtml = `<button data-id="${p.propertyId}" data-cost="${boostCost}" ${!hasEnoughCredits ? 'disabled' : ''} class="boost-btn font-semibold py-2 px-4 rounded-lg bg-purple-100 text-purple-700 hover:bg-purple-200 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed">🚀 Boost (${boostCost} Cr)</button>`;
                } else if (p.isBoosted) {
                    actionButtonHtml = `<div class="font-semibold py-2 px-4 rounded-lg bg-purple-600 text-white text-sm">🚀 Boosted</div>`;
                }

                return `
                <div class="bg-white p-4 rounded-2xl card-shadow border ${p.isBoosted ? 'boosted-card' : ''} flex items-center gap-4">
                    <img src="${p.imageUrls[0]}" onerror="this.onerror=null;this.src='https://placehold.co/100x100/f8fafc/cbd5e1?text=Image';" class="w-24 h-24 rounded-lg object-cover">
                    <div class="flex-grow">
                        <p class="font-bold text-slate-800">${p.address}</p>
                        <p class="text-sm text-slate-500">$${p.price.toLocaleString()} • ${p.verificationStatus}</p>
                    </div>
                    <div class="flex flex-col items-center gap-2">
                         <button data-id="${p.propertyId}" class="edit-btn font-semibold py-2 px-6 rounded-lg bg-slate-200 hover:bg-slate-300 text-slate-800">Edit</button>
                         ${actionButtonHtml}
                    </div>
                </div>`;
            }
            
            function createUserListingTableRow(p) {
                 return `
                    <tr class="border-b hover:bg-slate-50">
                        <td class="p-4">${p.address}</td>
                        <td class="p-4"><span class="px-2 py-1 text-xs font-semibold rounded-full ${p.verificationStatus === 'Verified' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${p.verificationStatus}</span></td>
                        <td class="p-4">$${p.price.toLocaleString()}</td>
                        <td class="p-4 text-center">
                            <button data-id="${p.propertyId}" class="edit-btn text-blue-600 hover:text-blue-800 font-semibold">Edit</button>
                        </td>
                    </tr>
                `;
            }


            function handleBoostListing(e) {
                const propId = e.target.dataset.id;
                const cost = parseInt(e.target.dataset.cost);
                const user = mockDatabase.users[currentUser.email];
                
                if (user && (user.credits || 0) >= cost) {
                    const prop = [...mockDatabase.properties, ...mockDatabase.investorProperties].find(p => p.propertyId === propId);
                    if (prop) {
                        user.credits -= cost;
                        prop.isBoosted = true;
                        showModal("Success!", `Listing for ${prop.address} has been boosted!`);
                        displayUserListings(); // Re-render to show updated state
                    }
                } else {
                    showModal("Error", "You do not have enough credits to boost this listing.");
                }
            }
            
            function renderChart(canvas, type, data, options = {}) {
                if (!canvas) return;
                const chart = new Chart(canvas, { 
                    type, 
                    data, 
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } },
                        ...options
                    }
                });
                activeCharts.push(chart);
            }

            // --- PROFILE & PAYMENTS PAGES ---
            function renderProfilePage() {
                const user = currentUser;
                mainContent.innerHTML = `
                    <div class="max-w-2xl mx-auto">
                        <div class="flex flex-col items-center mb-8">
                            <img src="${user.imageUrl}" class="w-32 h-32 rounded-full object-cover mb-4" onerror="this.onerror=null;this.src='https://placehold.co/128x128/f97316/ffffff?text=${user.name.charAt(0)}';">
                            <h1 class="text-3xl font-bold">${user.name}</h1>
                            <p class="text-slate-500">Joined 2025</p>
                        </div>
                        <div class="bg-white rounded-2xl shadow-lg border p-6 space-y-4 mb-6">
                            <h3 class="text-lg font-semibold">Personal Information</h3>
                            <div class="flex items-center"><span class="w-24 text-slate-500">Phone</span><span>${user.phone}</span></div>
                            <div class="flex items-center"><span class="w-24 text-slate-500">Email</span><span>${user.email}</span></div>
                            <div class="flex items-center"><span class="w-24 text-slate-500">Location</span><span>${user.location}</span></div>
                        </div>
                        <div class="bg-white rounded-2xl shadow-lg border p-6 space-y-1 mb-6">
                             <h3 class="text-lg font-semibold mb-2">Settings</h3>
                             <a href="#" data-page="notifications" class="nav-item flex justify-between items-center p-3 hover:bg-slate-50 rounded-lg"><span>Notifications</span><span>></span></a>
                             <a href="#" class="flex justify-between items-center p-3 hover:bg-slate-50 rounded-lg"><span>Privacy</span><span>></span></a>
                             <a href="#" class="flex justify-between items-center p-3 hover:bg-slate-50 rounded-lg"><span>Language</span><span>></span></a>
                             <a href="#" data-page="payments" class="nav-item flex justify-between items-center p-3 hover:bg-slate-50 rounded-lg"><span>Payments & Credits</span><span>></span></a>
                             <a href="#" class="flex justify-between items-center p-3 hover:bg-slate-50 rounded-lg"><span>Help & Support</span><span>></span></a>
                        </div>
                        <div class="mt-6">
                            <button id="profile-logout-button" class="w-full text-center py-3 px-4 border border-red-500 text-red-500 rounded-lg hover:bg-red-500 hover:text-white transition-colors">Logout</button>
                        </div>
                    </div>
                `;
                 document.querySelectorAll('.nav-item').forEach(item => item.addEventListener('click', (e) => { e.preventDefault(); navigateTo(item.dataset.page, item.dataset.id); }));
                 document.getElementById('profile-logout-button').addEventListener('click', () => {
                    currentUser = null;
                    sessionStorage.removeItem('loggedInUser');
                    appScreen.classList.add('hidden');
                    loginScreen.classList.remove('hidden');
                 });
            }

            function renderPaymentsPage() {
                const history = mockDatabase.paymentHistory[currentUser.uid] || [];
                mainContent.innerHTML = `
                    <div class="max-w-2xl mx-auto">
                        <button data-page="profile" class="nav-item text-slate-600 font-semibold mb-6">< Back to Profile</button>
                        <h1 class="text-3xl font-bold mb-6">Payments & Credits</h1>
                        <div class="bg-white rounded-2xl shadow-lg border p-6 mb-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="text-slate-500">Your Credits</p>
                                    <p class="text-4xl font-bold text-orange-600">${currentUser.credits || 0}</p>
                                </div>
                                <button class="btn-primary py-2 px-5 rounded-lg">Buy Credits</button>
                            </div>
                        </div>
                         <div class="bg-white rounded-2xl shadow-lg border p-6">
                             <h3 class="text-lg font-semibold mb-4">Payment History</h3>
                             <div class="space-y-3">
                                ${history.length > 0 ? history.map(item => `
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <p class="font-semibold">AED ${item.amount}</p>
                                            <p class="text-sm text-slate-500">${item.date}</p>
                                        </div>
                                        <span class="text-sm font-semibold text-green-600">${item.status}</span>
                                    </div>
                                `).join('') : '<p class="text-slate-500">No payment history found.</p>'}
                             </div>
                         </div>
                    </div>
                `;
                document.querySelectorAll('.nav-item').forEach(item => item.addEventListener('click', (e) => { e.preventDefault(); navigateTo(item.dataset.page, item.dataset.id); }));
            }

            function renderNotificationsPage() {
                const userNotifications = mockDatabase.notifications[currentUser.uid] || [];
                const newNotifications = userNotifications.filter(n => !n.read);
                const earlierNotifications = userNotifications.filter(n => n.read);

                const renderNotificationList = (notifications) => {
                    if (notifications.length === 0) return '<p class="text-slate-500 text-sm p-4">No notifications.</p>';
                    return notifications.map(n => `
                        <div class="flex items-start gap-4 p-4 hover:bg-slate-50 rounded-lg">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">${n.type === 'match' ? '🏠' : n.type === 'chat' ? '💬' : '🔔'}</div>
                            <div>
                                <p class="font-semibold">${n.title}</p>
                                <p class="text-sm text-slate-600">${n.text}</p>
                                <p class="text-xs text-slate-400 mt-1">${n.date}</p>
                            </div>
                        </div>
                    `).join('');
                };

                mainContent.innerHTML = `
                    <div class="max-w-3xl mx-auto">
                        <h1 class="text-3xl md:text-4xl font-extrabold text-slate-900 mb-6">Notifications</h1>
                        <div class="space-y-8">
                            <div>
                                <h2 class="text-xl font-bold mb-4">New</h2>
                                <div class="bg-white rounded-2xl shadow-lg border p-2 space-y-2">
                                    ${renderNotificationList(newNotifications)}
                                </div>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold mb-4">Earlier</h2>
                                <div class="bg-white rounded-2xl shadow-lg border p-2 space-y-2">
                                     ${renderNotificationList(earlierNotifications)}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
