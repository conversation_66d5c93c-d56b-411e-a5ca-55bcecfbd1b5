import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/property.dart';
import '../../domain/repositories/properties_repository.dart';
import '../datasources/properties_local_data_source.dart';
import '../datasources/properties_remote_data_source.dart';
import '../models/property_model.dart';

class PropertiesRepositoryImpl implements PropertiesRepository {
  final PropertiesRemoteDataSource remoteDataSource;
  final PropertiesLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  PropertiesRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Property>>> getProperties({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final propertyModels = await remoteDataSource.getProperties(
          type: type,
          status: status,
          minPrice: minPrice,
          maxPrice: maxPrice,
          minBedrooms: minBedrooms,
          maxBedrooms: maxBedrooms,
        );
        
        // Cache the results
        await localDataSource.cacheProperties(propertyModels);
        
        return Right(propertyModels.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      try {
        final cachedProperties = await localDataSource.getCachedProperties();
        return Right(cachedProperties.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(CacheFailure(e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, Property>> getPropertyDetails(String propertyId) async {
    if (await networkInfo.isConnected) {
      try {
        final propertyModel = await remoteDataSource.getPropertyById(propertyId);
        await localDataSource.cacheProperty(propertyModel);
        return Right(propertyModel.toEntity());
      } catch (e) {
        if (e.toString().contains('not found')) {
          return const Left(PropertyNotFoundFailure());
        }
        return Left(ServerFailure(e.toString()));
      }
    } else {
      try {
        final cachedProperty = await localDataSource.getCachedProperty(propertyId);
        if (cachedProperty != null) {
          return Right(cachedProperty.toEntity());
        } else {
          return const Left(PropertyNotFoundFailure());
        }
      } catch (e) {
        return Left(CacheFailure(e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, List<Property>>> searchProperties(String query) async {
    if (await networkInfo.isConnected) {
      try {
        final propertyModels = await remoteDataSource.searchProperties(query);
        return Right(propertyModels.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Property>> addProperty(Property property) async {
    if (await networkInfo.isConnected) {
      try {
        final propertyModel = PropertyModel.fromEntity(property);
        final result = await remoteDataSource.addProperty(propertyModel);
        return Right(result.toEntity());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Property>> updateProperty(Property property) async {
    if (await networkInfo.isConnected) {
      try {
        final propertyModel = PropertyModel.fromEntity(property);
        final result = await remoteDataSource.updateProperty(propertyModel);
        await localDataSource.cacheProperty(result);
        return Right(result.toEntity());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProperty(String propertyId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteProperty(propertyId);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }
}
