part of 'chat_bloc.dart';

abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object?> get props => [];
}

class ChatLoadConversations extends ChatEvent {
  final String? userId;
  final int? limit;
  final int? offset;

  const ChatLoadConversations({
    this.userId,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [userId, limit, offset];
}

class ChatCreateConversation extends ChatEvent {
  final String title;
  final String? userId;
  final String? firstMessage;

  const ChatCreateConversation({
    required this.title,
    this.userId,
    this.firstMessage,
  });

  @override
  List<Object?> get props => [title, userId, firstMessage];
}

class ChatSelectConversation extends ChatEvent {
  final Conversation conversation;

  const ChatSelectConversation({required this.conversation});

  @override
  List<Object> get props => [conversation];
}

class ChatSendMessage extends ChatEvent {
  final String content;
  final Map<String, dynamic>? metadata;
  final List<String>? attachments;

  const ChatSendMessage({
    required this.content,
    this.metadata,
    this.attachments,
  });

  @override
  List<Object?> get props => [content, metadata, attachments];
}

class ChatUpdateConversation extends ChatEvent {
  final Conversation conversation;

  const ChatUpdateConversation({required this.conversation});

  @override
  List<Object> get props => [conversation];
}

class ChatDeleteConversation extends ChatEvent {
  final String conversationId;

  const ChatDeleteConversation({required this.conversationId});

  @override
  List<Object> get props => [conversationId];
}

class ChatPinConversation extends ChatEvent {
  final String conversationId;
  final bool isPinned;

  const ChatPinConversation({
    required this.conversationId,
    required this.isPinned,
  });

  @override
  List<Object> get props => [conversationId, isPinned];
}

class ChatFavoriteConversation extends ChatEvent {
  final String conversationId;
  final bool isFavorite;

  const ChatFavoriteConversation({
    required this.conversationId,
    required this.isFavorite,
  });

  @override
  List<Object> get props => [conversationId, isFavorite];
}

class ChatRenameConversation extends ChatEvent {
  final String conversationId;
  final String newTitle;

  const ChatRenameConversation({
    required this.conversationId,
    required this.newTitle,
  });

  @override
  List<Object> get props => [conversationId, newTitle];
}

class ChatSearchConversations extends ChatEvent {
  final String query;

  const ChatSearchConversations({required this.query});

  @override
  List<Object> get props => [query];
}

class ChatClearSearch extends ChatEvent {
  const ChatClearSearch();
}

class ChatExportConversation extends ChatEvent {
  final String conversationId;
  final String format;

  const ChatExportConversation({
    required this.conversationId,
    required this.format,
  });

  @override
  List<Object> get props => [conversationId, format];
}

class ChatShareConversation extends ChatEvent {
  final String conversationId;

  const ChatShareConversation({required this.conversationId});

  @override
  List<Object> get props => [conversationId];
}

class ChatRegenerateMessage extends ChatEvent {
  final String messageId;

  const ChatRegenerateMessage({required this.messageId});

  @override
  List<Object> get props => [messageId];
}

class ChatCopyMessage extends ChatEvent {
  final String messageId;

  const ChatCopyMessage({required this.messageId});

  @override
  List<Object> get props => [messageId];
}

class ChatDeleteMessage extends ChatEvent {
  final String messageId;

  const ChatDeleteMessage({required this.messageId});

  @override
  List<Object> get props => [messageId];
}
