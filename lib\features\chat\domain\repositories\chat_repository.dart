import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/conversation.dart';
import '../entities/message.dart';

abstract class ChatRepository {
  // Conversation Management
  Future<Either<Failure, List<Conversation>>> getConversations({
    String? userId,
    int? limit,
    int? offset,
  });
  
  Future<Either<Failure, Conversation>> getConversation(String conversationId);
  
  Future<Either<Failure, Conversation>> createConversation({
    required String title,
    String? userId,
    String? firstMessage,
  });
  
  Future<Either<Failure, Conversation>> updateConversation(Conversation conversation);
  
  Future<Either<Failure, void>> deleteConversation(String conversationId);
  
  Future<Either<Failure, Conversation>> pinConversation(String conversationId, bool isPinned);
  
  Future<Either<Failure, Conversation>> favoriteConversation(String conversationId, bool isFavorite);
  
  Future<Either<Failure, Conversation>> renameConversation(String conversationId, String newTitle);
  
  // Message Management
  Future<Either<Failure, List<Message>>> getMessages({
    required String conversationId,
    int? limit,
    int? offset,
  });
  
  Future<Either<Failure, Message>> sendMessage({
    required String conversationId,
    required String content,
    required MessageType type,
    Map<String, dynamic>? metadata,
    List<String>? attachments,
  });
  
  Future<Either<Failure, Message>> updateMessage(Message message);
  
  Future<Either<Failure, void>> deleteMessage(String messageId);
  
  Future<Either<Failure, Message>> regenerateMessage(String messageId);
  
  // Search Functionality
  Future<Either<Failure, List<Conversation>>> searchConversations({
    required String query,
    String? userId,
    int? limit,
  });
  
  Future<Either<Failure, List<Message>>> searchMessages({
    required String query,
    String? conversationId,
    String? userId,
    int? limit,
  });
  
  // Export Functionality
  Future<Either<Failure, String>> exportConversation({
    required String conversationId,
    required String format, // 'txt', 'pdf', 'json'
  });
  
  Future<Either<Failure, String>> shareConversation(String conversationId);
  
  // Bulk Operations
  Future<Either<Failure, void>> deleteMultipleConversations(List<String> conversationIds);
  
  Future<Either<Failure, void>> archiveConversations(List<String> conversationIds);
  
  // Analytics
  Future<Either<Failure, Map<String, dynamic>>> getConversationStats(String? userId);
  
  // Offline Support
  Future<Either<Failure, void>> syncConversations();
  
  Future<Either<Failure, List<Conversation>>> getCachedConversations();
}
