import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/dashboard/presentation/pages/investor_dashboard_page.dart';
import '../../features/dashboard/presentation/pages/agent_dashboard_page.dart';
import '../../features/properties/presentation/pages/properties_page.dart';
import '../../features/properties/presentation/pages/property_details_page.dart';
import '../../features/properties/presentation/pages/property_finder_page.dart';
import '../../features/properties/presentation/pages/manage_properties_page.dart';
import '../../features/agents/presentation/pages/agents_page.dart';
import '../../features/agents/presentation/pages/agent_profile_page.dart';
import '../../features/agents/presentation/pages/agent_leads_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/notifications/presentation/pages/notifications_page.dart';
import '../../features/admin/presentation/pages/admin_dashboard_page.dart';
import '../../features/admin/presentation/pages/user_management_page.dart';
import '../../features/admin/presentation/pages/feature_control_page.dart';
import '../../features/admin/presentation/pages/agent_verification_page.dart';
import '../../features/admin/presentation/pages/property_verification_page.dart';
import '../../features/shared/presentation/pages/main_wrapper_page.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    routes: [
      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      // Main App Routes with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainWrapperPage(child: child),
        routes: [
          // Dashboard Routes
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardPage(),
          ),
          GoRoute(
            path: '/investor-dashboard',
            name: 'investor-dashboard',
            builder: (context, state) => const InvestorDashboardPage(),
          ),
          GoRoute(
            path: '/agent-dashboard',
            name: 'agent-dashboard',
            builder: (context, state) => const AgentDashboardPage(),
          ),
          
          // Properties Routes
          GoRoute(
            path: '/properties',
            name: 'properties',
            builder: (context, state) => const PropertiesPage(),
          ),
          GoRoute(
            path: '/property-finder',
            name: 'property-finder',
            builder: (context, state) => const PropertyFinderPage(),
          ),
          GoRoute(
            path: '/manage-properties',
            name: 'manage-properties',
            builder: (context, state) => const ManagePropertiesPage(),
          ),
          
          // Agents Routes
          GoRoute(
            path: '/agents',
            name: 'agents',
            builder: (context, state) => const AgentsPage(),
          ),
          GoRoute(
            path: '/agent-leads',
            name: 'agent-leads',
            builder: (context, state) => const AgentLeadsPage(),
          ),
          
          // Profile and Settings
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: '/notifications',
            name: 'notifications',
            builder: (context, state) => const NotificationsPage(),
          ),
          
          // Admin Routes
          GoRoute(
            path: '/admin',
            name: 'admin',
            builder: (context, state) => const AdminDashboardPage(),
          ),
          GoRoute(
            path: '/admin/users',
            name: 'admin-users',
            builder: (context, state) => const UserManagementPage(),
          ),
          GoRoute(
            path: '/admin/features',
            name: 'admin-features',
            builder: (context, state) => const FeatureControlPage(),
          ),
          GoRoute(
            path: '/admin/agent-verification',
            name: 'admin-agent-verification',
            builder: (context, state) => const AgentVerificationPage(),
          ),
          GoRoute(
            path: '/admin/property-verification',
            name: 'admin-property-verification',
            builder: (context, state) => const PropertyVerificationPage(),
          ),
        ],
      ),
      
      // Detail Routes (Full Screen)
      GoRoute(
        path: '/property/:id',
        name: 'property-details',
        builder: (context, state) {
          final propertyId = state.pathParameters['id']!;
          return PropertyDetailsPage(propertyId: propertyId);
        },
      ),
      GoRoute(
        path: '/agent/:id',
        name: 'agent-profile',
        builder: (context, state) {
          final agentId = state.pathParameters['id']!;
          return AgentProfilePage(agentId: agentId);
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
}
