import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/entities/conversation.dart';
import '../../domain/entities/message.dart';
import '../../domain/usecases/get_conversations.dart';
import '../../domain/usecases/create_conversation.dart';
import '../../domain/usecases/send_message.dart';

part 'chat_event.dart';
part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final GetConversations getConversations;
  final CreateConversation createConversation;
  final SendMessage sendMessage;

  ChatBloc({
    required this.getConversations,
    required this.createConversation,
    required this.sendMessage,
  }) : super(ChatInitial()) {
    on<ChatLoadConversations>(_onLoadConversations);
    on<ChatCreateConversation>(_onCreateConversation);
    on<ChatSelectConversation>(_onSelectConversation);
    on<ChatSendMessage>(_onSendMessage);
    on<ChatUpdateConversation>(_onUpdateConversation);
    on<ChatDeleteConversation>(_onDeleteConversation);
    on<ChatPinConversation>(_onPinConversation);
    on<ChatFavoriteConversation>(_onFavoriteConversation);
    on<ChatRenameConversation>(_onRenameConversation);
    on<ChatSearchConversations>(_onSearchConversations);
    on<ChatClearSearch>(_onClearSearch);
  }

  Future<void> _onLoadConversations(
    ChatLoadConversations event,
    Emitter<ChatState> emit,
  ) async {
    emit(ChatLoading());

    final result = await getConversations(GetConversationsParams(
      userId: event.userId,
      limit: event.limit,
      offset: event.offset,
    ));

    result.fold(
      (failure) => emit(ChatError(message: failure.message)),
      (conversations) => emit(ChatLoaded(
        conversations: conversations,
        selectedConversation: null,
        isSearching: false,
        searchQuery: '',
        searchResults: [],
      )),
    );
  }

  Future<void> _onCreateConversation(
    ChatCreateConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      emit(currentState.copyWith(isLoading: true));

      final result = await createConversation(CreateConversationParams(
        title: event.title,
        userId: event.userId,
        firstMessage: event.firstMessage,
      ));

      result.fold(
        (failure) => emit(currentState.copyWith(
          isLoading: false,
          error: failure.message,
        )),
        (conversation) {
          final updatedConversations = [conversation, ...currentState.conversations];
          emit(currentState.copyWith(
            conversations: updatedConversations,
            selectedConversation: conversation,
            isLoading: false,
            error: null,
          ));
        },
      );
    }
  }

  Future<void> _onSelectConversation(
    ChatSelectConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      emit(currentState.copyWith(selectedConversation: event.conversation));
    }
  }

  Future<void> _onSendMessage(
    ChatSendMessage event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      if (currentState.selectedConversation == null) return;

      // Add user message immediately
      final userMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: event.content,
        type: MessageType.user,
        timestamp: DateTime.now(),
        conversationId: currentState.selectedConversation!.id,
        status: MessageStatus.sending,
      );

      final updatedMessages = [
        ...currentState.selectedConversation!.messages,
        userMessage,
      ];

      final updatedConversation = currentState.selectedConversation!.copyWith(
        messages: updatedMessages,
        messageCount: updatedMessages.length,
        lastMessagePreview: event.content,
        lastMessageAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      emit(currentState.copyWith(selectedConversation: updatedConversation));

      // Send message to repository
      final result = await sendMessage(SendMessageParams(
        conversationId: currentState.selectedConversation!.id,
        content: event.content,
        type: MessageType.user,
        metadata: event.metadata,
        attachments: event.attachments,
      ));

      result.fold(
        (failure) {
          // Update message status to failed
          final failedMessage = userMessage.copyWith(status: MessageStatus.failed);
          final failedMessages = updatedMessages.map((m) => 
            m.id == userMessage.id ? failedMessage : m
          ).toList();
          
          final failedConversation = updatedConversation.copyWith(messages: failedMessages);
          emit(currentState.copyWith(selectedConversation: failedConversation));
        },
        (sentMessage) {
          // Update message status to sent
          final sentMessages = updatedMessages.map((m) => 
            m.id == userMessage.id ? sentMessage : m
          ).toList();
          
          final sentConversation = updatedConversation.copyWith(messages: sentMessages);
          emit(currentState.copyWith(selectedConversation: sentConversation));

          // Simulate AI response (in real app, this would be handled by the AI service)
          _simulateAIResponse(sentConversation, emit, currentState);
        },
      );
    }
  }

  Future<void> _simulateAIResponse(
    Conversation conversation,
    Emitter<ChatState> emit,
    ChatLoaded currentState,
  ) async {
    await Future.delayed(const Duration(seconds: 1));

    final aiMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: _generateAIResponse(conversation.messages.last.content),
      type: MessageType.assistant,
      timestamp: DateTime.now(),
      conversationId: conversation.id,
    );

    final updatedMessages = [...conversation.messages, aiMessage];
    final updatedConversation = conversation.copyWith(
      messages: updatedMessages,
      messageCount: updatedMessages.length,
      lastMessagePreview: aiMessage.content,
      lastMessageAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    emit(currentState.copyWith(selectedConversation: updatedConversation));
  }

  String _generateAIResponse(String userMessage) {
    // Simple AI response simulation for property-related queries
    final message = userMessage.toLowerCase();
    
    if (message.contains('property') || message.contains('house') || message.contains('apartment')) {
      return "I'd be happy to help you find properties! Based on your query, I can search for properties that match your criteria. What specific features are you looking for? (bedrooms, location, price range, etc.)";
    } else if (message.contains('price') || message.contains('cost') || message.contains('budget')) {
      return "I can help you with pricing information. What's your budget range? I can show you properties within your price range and provide market insights.";
    } else if (message.contains('location') || message.contains('area') || message.contains('neighborhood')) {
      return "Location is key! Which areas are you interested in? I can provide information about different neighborhoods, amenities, and property availability.";
    } else {
      return "Thank you for your message! I'm here to help you with all your property needs. Feel free to ask about properties, pricing, locations, or any other real estate questions.";
    }
  }

  Future<void> _onUpdateConversation(
    ChatUpdateConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedConversations = currentState.conversations.map((c) =>
        c.id == event.conversation.id ? event.conversation : c
      ).toList();

      emit(currentState.copyWith(
        conversations: updatedConversations,
        selectedConversation: currentState.selectedConversation?.id == event.conversation.id
            ? event.conversation
            : currentState.selectedConversation,
      ));
    }
  }

  Future<void> _onDeleteConversation(
    ChatDeleteConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedConversations = currentState.conversations
          .where((c) => c.id != event.conversationId)
          .toList();

      final updatedSelectedConversation = 
          currentState.selectedConversation?.id == event.conversationId
              ? null
              : currentState.selectedConversation;

      emit(currentState.copyWith(
        conversations: updatedConversations,
        selectedConversation: updatedSelectedConversation,
      ));
    }
  }

  Future<void> _onPinConversation(
    ChatPinConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedConversations = currentState.conversations.map((c) =>
        c.id == event.conversationId 
            ? c.copyWith(isPinned: event.isPinned, updatedAt: DateTime.now())
            : c
      ).toList();

      // Sort conversations (pinned first)
      updatedConversations.sort((a, b) {
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        return b.updatedAt.compareTo(a.updatedAt);
      });

      emit(currentState.copyWith(conversations: updatedConversations));
    }
  }

  Future<void> _onFavoriteConversation(
    ChatFavoriteConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedConversations = currentState.conversations.map((c) =>
        c.id == event.conversationId 
            ? c.copyWith(isFavorite: event.isFavorite, updatedAt: DateTime.now())
            : c
      ).toList();

      emit(currentState.copyWith(conversations: updatedConversations));
    }
  }

  Future<void> _onRenameConversation(
    ChatRenameConversation event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedConversations = currentState.conversations.map((c) =>
        c.id == event.conversationId 
            ? c.copyWith(title: event.newTitle, updatedAt: DateTime.now())
            : c
      ).toList();

      final updatedSelectedConversation = 
          currentState.selectedConversation?.id == event.conversationId
              ? currentState.selectedConversation!.copyWith(title: event.newTitle)
              : currentState.selectedConversation;

      emit(currentState.copyWith(
        conversations: updatedConversations,
        selectedConversation: updatedSelectedConversation,
      ));
    }
  }

  Future<void> _onSearchConversations(
    ChatSearchConversations event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      if (event.query.isEmpty) {
        emit(currentState.copyWith(
          isSearching: false,
          searchQuery: '',
          searchResults: [],
        ));
        return;
      }

      emit(currentState.copyWith(
        isSearching: true,
        searchQuery: event.query,
      ));

      // Simple local search implementation
      final searchResults = currentState.conversations.where((c) =>
        c.title.toLowerCase().contains(event.query.toLowerCase()) ||
        (c.description?.toLowerCase().contains(event.query.toLowerCase()) ?? false)
      ).toList();

      emit(currentState.copyWith(searchResults: searchResults));
    }
  }

  Future<void> _onClearSearch(
    ChatClearSearch event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      emit(currentState.copyWith(
        isSearching: false,
        searchQuery: '',
        searchResults: [],
      ));
    }
  }
}
