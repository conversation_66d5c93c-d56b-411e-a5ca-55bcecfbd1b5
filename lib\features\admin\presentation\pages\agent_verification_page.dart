import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class AgentVerificationPage extends StatelessWidget {
  const AgentVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Agent Verification',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'Agent Verification',
          message: 'Agent verification tools will be available here.',
          icon: Icons.verified_user_outlined,
        ),
      ),
    );
  }
}
