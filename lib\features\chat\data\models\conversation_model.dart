import 'package:hive/hive.dart';
import '../../domain/entities/conversation.dart';
import 'message_model.dart';

part 'conversation_model.g.dart';

@HiveType(typeId: 2)
class ConversationModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final DateTime createdAt;

  @HiveField(4)
  final DateTime updatedAt;

  @HiveField(5)
  final String status;

  @HiveField(6)
  final List<MessageModel> messages;

  @HiveField(7)
  final bool isPinned;

  @HiveField(8)
  final bool isFavorite;

  @HiveField(9)
  final Map<String, dynamic>? metadata;

  @HiveField(10)
  final String? userId;

  @HiveField(11)
  final int messageCount;

  @HiveField(12)
  final String? lastMessagePreview;

  @HiveField(13)
  final DateTime? lastMessageAt;

  @HiveField(14)
  final List<String>? tags;

  ConversationModel({
    required this.id,
    required this.title,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    this.messages = const [],
    this.isPinned = false,
    this.isFavorite = false,
    this.metadata,
    this.userId,
    required this.messageCount,
    this.lastMessagePreview,
    this.lastMessageAt,
    this.tags,
  });

  factory ConversationModel.fromEntity(Conversation conversation) {
    return ConversationModel(
      id: conversation.id,
      title: conversation.title,
      description: conversation.description,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      status: conversation.status.name,
      messages: conversation.messages
          .map((m) => MessageModel.fromEntity(m))
          .toList(),
      isPinned: conversation.isPinned,
      isFavorite: conversation.isFavorite,
      metadata: conversation.metadata,
      userId: conversation.userId,
      messageCount: conversation.messageCount,
      lastMessagePreview: conversation.lastMessagePreview,
      lastMessageAt: conversation.lastMessageAt,
      tags: conversation.tags,
    );
  }

  Conversation toEntity() {
    return Conversation(
      id: id,
      title: title,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
      status: ConversationStatus.values.firstWhere(
        (e) => e.name == status,
        orElse: () => ConversationStatus.active,
      ),
      messages: messages.map((m) => m.toEntity()).toList(),
      isPinned: isPinned,
      isFavorite: isFavorite,
      metadata: metadata,
      userId: userId,
      messageCount: messageCount,
      lastMessagePreview: lastMessagePreview,
      lastMessageAt: lastMessageAt,
      tags: tags,
    );
  }

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      status: json['status'] as String,
      messages: (json['messages'] as List<dynamic>?)
              ?.map((m) => MessageModel.fromJson(m as Map<String, dynamic>))
              .toList() ??
          [],
      isPinned: json['isPinned'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
      userId: json['userId'] as String?,
      messageCount: json['messageCount'] as int? ?? 0,
      lastMessagePreview: json['lastMessagePreview'] as String?,
      lastMessageAt: json['lastMessageAt'] != null
          ? DateTime.parse(json['lastMessageAt'] as String)
          : null,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status,
      'messages': messages.map((m) => m.toJson()).toList(),
      'isPinned': isPinned,
      'isFavorite': isFavorite,
      'metadata': metadata,
      'userId': userId,
      'messageCount': messageCount,
      'lastMessagePreview': lastMessagePreview,
      'lastMessageAt': lastMessageAt?.toIso8601String(),
      'tags': tags,
    };
  }
}
