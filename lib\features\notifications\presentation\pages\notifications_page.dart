import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Notifications',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'No Notifications',
          message: 'You\'re all caught up! New notifications will appear here.',
          icon: Icons.notifications_outlined,
        ),
      ),
    );
  }
}
