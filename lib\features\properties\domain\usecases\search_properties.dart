import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/property.dart';
import '../repositories/properties_repository.dart';

class SearchProperties implements UseCase<List<Property>, SearchPropertiesParams> {
  final PropertiesRepository repository;

  SearchProperties(this.repository);

  @override
  Future<Either<Failure, List<Property>>> call(SearchPropertiesParams params) async {
    return await repository.searchProperties(params.query);
  }
}

class SearchPropertiesParams extends Equatable {
  final String query;

  const SearchPropertiesParams({required this.query});

  @override
  List<Object> get props => [query];
}
