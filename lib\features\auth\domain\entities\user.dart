import 'package:equatable/equatable.dart';

enum UserRole {
  admin,
  agent,
  investor,
  regular,
}

enum UserStatus {
  active,
  suspended,
  pending,
}

enum VerificationStatus {
  verified,
  pending,
  rejected,
}

class User extends Equatable {
  final String uid;
  final String name;
  final String email;
  final String phone;
  final String location;
  final UserRole userRole;
  final UserStatus status;
  final VerificationStatus? verificationStatus;
  final String imageUrl;
  final int? credits;
  final List<String>? recentlyViewed;
  final List<String>? specialization;
  final List<String>? languages;
  final double? rating;
  final int? experience;
  final int? propertiesSold;

  const User({
    required this.uid,
    required this.name,
    required this.email,
    required this.phone,
    required this.location,
    required this.userRole,
    required this.status,
    required this.imageUrl,
    this.verificationStatus,
    this.credits,
    this.recentlyViewed,
    this.specialization,
    this.languages,
    this.rating,
    this.experience,
    this.propertiesSold,
  });

  User copyWith({
    String? uid,
    String? name,
    String? email,
    String? phone,
    String? location,
    UserRole? userRole,
    UserStatus? status,
    VerificationStatus? verificationStatus,
    String? imageUrl,
    int? credits,
    List<String>? recentlyViewed,
    List<String>? specialization,
    List<String>? languages,
    double? rating,
    int? experience,
    int? propertiesSold,
  }) {
    return User(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      userRole: userRole ?? this.userRole,
      status: status ?? this.status,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      imageUrl: imageUrl ?? this.imageUrl,
      credits: credits ?? this.credits,
      recentlyViewed: recentlyViewed ?? this.recentlyViewed,
      specialization: specialization ?? this.specialization,
      languages: languages ?? this.languages,
      rating: rating ?? this.rating,
      experience: experience ?? this.experience,
      propertiesSold: propertiesSold ?? this.propertiesSold,
    );
  }

  @override
  List<Object?> get props => [
        uid,
        name,
        email,
        phone,
        location,
        userRole,
        status,
        verificationStatus,
        imageUrl,
        credits,
        recentlyViewed,
        specialization,
        languages,
        rating,
        experience,
        propertiesSold,
      ];

  // Helper methods
  bool get isAgent => userRole == UserRole.agent;
  bool get isInvestor => userRole == UserRole.investor;
  bool get isAdmin => userRole == UserRole.admin;
  bool get isRegular => userRole == UserRole.regular;
  
  bool get isVerified => verificationStatus == VerificationStatus.verified;
  bool get isPending => verificationStatus == VerificationStatus.pending;
  bool get isActive => status == UserStatus.active;
  
  String get roleDisplayName {
    switch (userRole) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.agent:
        return 'Real Estate Agent';
      case UserRole.investor:
        return 'Property Investor';
      case UserRole.regular:
        return 'User';
    }
  }
  
  String get statusDisplayName {
    switch (status) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.suspended:
        return 'Suspended';
      case UserStatus.pending:
        return 'Pending';
    }
  }
  
  String get verificationDisplayName {
    if (verificationStatus == null) return '';
    switch (verificationStatus!) {
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.pending:
        return 'Pending Verification';
      case VerificationStatus.rejected:
        return 'Verification Rejected';
    }
  }
}
