import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../bloc/auth_bloc.dart';

class QuickAccessButtons extends StatelessWidget {
  const QuickAccessButtons({super.key});

  static const List<Map<String, String>> _quickAccessUsers = [
    {'email': '<EMAIL>', 'label': 'Admin'},
    {'email': '<EMAIL>', 'label': 'User'},
    {'email': '<EMAIL>', 'label': 'Investor'},
    {'email': '<EMAIL>', 'label': 'Agent (Verified)'},
    {'email': '<EMAIL>', 'label': 'Agent (Pending)'},
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: _quickAccessUsers.length,
          itemBuilder: (context, index) {
            final user = _quickAccessUsers[index];
            final isLastItem = index == _quickAccessUsers.length - 1;
            
            return SizedBox(
              width: isLastItem ? double.infinity : null,
              child: OutlinedButton(
                onPressed: isLoading
                    ? null
                    : () => _loginWithQuickAccess(context, user['email']!),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  side: BorderSide(
                    color: isLoading 
                        ? AppColors.outline.withOpacity(0.5)
                        : AppColors.outline,
                  ),
                  foregroundColor: isLoading 
                      ? AppColors.onSurface.withOpacity(0.5)
                      : AppColors.onSurface,
                ),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    user['label']!,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _loginWithQuickAccess(BuildContext context, String email) {
    context.read<AuthBloc>().add(
      AuthLoginRequested(
        email: email,
        password: 'password', // Default password for demo
      ),
    );
  }
}
