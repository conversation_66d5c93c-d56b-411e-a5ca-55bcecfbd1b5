import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Data Sources
import '../../features/auth/data/datasources/auth_local_data_source.dart';
import '../../features/auth/data/datasources/auth_remote_data_source.dart';
import '../../features/properties/data/datasources/properties_local_data_source.dart';
import '../../features/properties/data/datasources/properties_remote_data_source.dart';
import '../../features/agents/data/datasources/agents_local_data_source.dart';
import '../../features/agents/data/datasources/agents_remote_data_source.dart';

// Repositories
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/properties/data/repositories/properties_repository_impl.dart';
import '../../features/properties/domain/repositories/properties_repository.dart';
import '../../features/agents/data/repositories/agents_repository_impl.dart';
import '../../features/agents/domain/repositories/agents_repository.dart';

// Use Cases
import '../../features/auth/domain/usecases/login_user.dart';
import '../../features/auth/domain/usecases/logout_user.dart';
import '../../features/auth/domain/usecases/get_current_user.dart';
import '../../features/properties/domain/usecases/get_properties.dart';
import '../../features/properties/domain/usecases/get_property_details.dart';
import '../../features/properties/domain/usecases/search_properties.dart';
import '../../features/properties/domain/usecases/add_property.dart';
import '../../features/properties/domain/usecases/update_property.dart';
import '../../features/properties/domain/usecases/delete_property.dart';
import '../../features/agents/domain/usecases/get_agents.dart';
import '../../features/agents/domain/usecases/get_agent_details.dart';
import '../../features/chat/domain/usecases/get_conversations.dart';
import '../../features/chat/domain/usecases/create_conversation.dart';
import '../../features/chat/domain/usecases/send_message.dart';
import '../../features/chat/data/datasources/chat_local_datasource.dart';
import '../../features/chat/data/repositories/chat_repository_impl.dart';
import '../../features/chat/domain/repositories/chat_repository.dart';

// BLoCs
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/properties/presentation/bloc/properties_bloc.dart';
import '../../features/dashboard/presentation/bloc/dashboard_bloc.dart';
import '../../features/agents/presentation/bloc/agents_bloc.dart';
import '../../features/chat/presentation/bloc/chat_bloc.dart';

// Core
import '../network/network_info.dart';
import '../services/mock_data_service.dart';

final sl = GetIt.instance;

Future<void> init() async {
  //! Features - Auth
  // Bloc
  sl.registerFactory(
    () => AuthBloc(
      loginUser: sl(),
      logoutUser: sl(),
      getCurrentUser: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => LoginUser(sl()));
  sl.registerLazySingleton(() => LogoutUser(sl()));
  sl.registerLazySingleton(() => GetCurrentUser(sl()));

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(dio: sl()),
  );

  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );

  //! Features - Properties
  // Bloc
  sl.registerFactory(
    () => PropertiesBloc(
      getProperties: sl(),
      getPropertyDetails: sl(),
      searchProperties: sl(),
      addProperty: sl(),
      updateProperty: sl(),
      deleteProperty: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetProperties(sl()));
  sl.registerLazySingleton(() => GetPropertyDetails(sl()));
  sl.registerLazySingleton(() => SearchProperties(sl()));
  sl.registerLazySingleton(() => AddProperty(sl()));
  sl.registerLazySingleton(() => UpdateProperty(sl()));
  sl.registerLazySingleton(() => DeleteProperty(sl()));

  // Repository
  sl.registerLazySingleton<PropertiesRepository>(
    () => PropertiesRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<PropertiesRemoteDataSource>(
    () => PropertiesRemoteDataSourceImpl(dio: sl()),
  );

  sl.registerLazySingleton<PropertiesLocalDataSource>(
    () => PropertiesLocalDataSourceImpl(sharedPreferences: sl()),
  );

  //! Features - Agents
  // Bloc
  sl.registerFactory(
    () => AgentsBloc(
      getAgents: sl(),
      getAgentDetails: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAgents(sl()));
  sl.registerLazySingleton(() => GetAgentDetails(sl()));

  // Repository
  sl.registerLazySingleton<AgentsRepository>(
    () => AgentsRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AgentsRemoteDataSource>(
    () => AgentsRemoteDataSourceImpl(dio: sl()),
  );

  sl.registerLazySingleton<AgentsLocalDataSource>(
    () => AgentsLocalDataSourceImpl(sharedPreferences: sl()),
  );

  //! Features - Dashboard
  // Bloc
  sl.registerFactory(
    () => DashboardBloc(
      getProperties: sl(),
      getAgents: sl(),
    ),
  );

  //! Features - Chat
  // Bloc
  sl.registerFactory(
    () => ChatBloc(
      getConversations: sl(),
      createConversation: sl(),
      sendMessage: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetConversations(sl()));
  sl.registerLazySingleton(() => CreateConversation(sl()));
  sl.registerLazySingleton(() => SendMessage(sl()));

  // Repository
  sl.registerLazySingleton<ChatRepository>(
    () => ChatRepositoryImpl(
      localDataSource: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<ChatLocalDataSource>(
    () => ChatLocalDataSourceImpl(),
  );

  //! Core
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());
  sl.registerLazySingleton<MockDataService>(() => MockDataService());

  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  
  sl.registerLazySingleton(() => Dio());
}
