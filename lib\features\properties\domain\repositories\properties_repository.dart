import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/property.dart';

abstract class PropertiesRepository {
  Future<Either<Failure, List<Property>>> getProperties({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
  });
  
  Future<Either<Failure, Property>> getPropertyDetails(String propertyId);
  
  Future<Either<Failure, List<Property>>> searchProperties(String query);
  
  Future<Either<Failure, Property>> addProperty(Property property);
  
  Future<Either<Failure, Property>> updateProperty(Property property);
  
  Future<Either<Failure, void>> deleteProperty(String propertyId);
}
