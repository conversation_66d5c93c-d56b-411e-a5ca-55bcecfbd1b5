import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/property.dart';
import '../repositories/properties_repository.dart';

class UpdateProperty implements UseCase<Property, UpdatePropertyParams> {
  final PropertiesRepository repository;

  UpdateProperty(this.repository);

  @override
  Future<Either<Failure, Property>> call(UpdatePropertyParams params) async {
    return await repository.updateProperty(params.property);
  }
}

class UpdatePropertyParams extends Equatable {
  final Property property;

  const UpdatePropertyParams({required this.property});

  @override
  List<Object> get props => [property];
}
