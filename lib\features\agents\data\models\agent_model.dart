import '../../domain/entities/agent.dart';
import '../../../auth/domain/entities/user.dart';

// part 'agent_model.g.dart'; // Uncomment after running build_runner

// @JsonSerializable() // Uncomment after running build_runner
class AgentModel extends Agent {
  const AgentModel({
    required super.agentId,
    required super.name,
    required super.email,
    required super.phone,
    required super.location,
    required super.verificationStatus,
    required super.imageUrl,
    required super.specialization,
    required super.languages,
    required super.rating,
    required super.experience,
    required super.propertiesSold,
    super.credits,
    super.bio,
    super.company,
    super.licenseNumber,
    super.joinedDate,
  });

  factory AgentModel.fromJson(Map<String, dynamic> json) {
    return AgentModel(
      agentId: json['agentId'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      location: json['location'] as String,
      verificationStatus: VerificationStatus.values.firstWhere((e) => e.toString() == 'VerificationStatus.${json['verificationStatus']}'),
      imageUrl: json['imageUrl'] as String,
      specialization: (json['specialization'] as List<dynamic>).cast<String>(),
      languages: (json['languages'] as List<dynamic>).cast<String>(),
      rating: (json['rating'] as num).toDouble(),
      experience: json['experience'] as int,
      propertiesSold: json['propertiesSold'] as int,
      credits: json['credits'] as int?,
      bio: json['bio'] as String?,
      company: json['company'] as String?,
      licenseNumber: json['licenseNumber'] as String?,
      joinedDate: json['joinedDate'] != null ? DateTime.parse(json['joinedDate'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agentId': agentId,
      'name': name,
      'email': email,
      'phone': phone,
      'location': location,
      'verificationStatus': verificationStatus.toString().split('.').last,
      'imageUrl': imageUrl,
      'specialization': specialization,
      'languages': languages,
      'rating': rating,
      'experience': experience,
      'propertiesSold': propertiesSold,
      'credits': credits,
      'bio': bio,
      'company': company,
      'licenseNumber': licenseNumber,
      'joinedDate': joinedDate?.toIso8601String(),
    };
  }

  factory AgentModel.fromEntity(Agent agent) {
    return AgentModel(
      agentId: agent.agentId,
      name: agent.name,
      email: agent.email,
      phone: agent.phone,
      location: agent.location,
      verificationStatus: agent.verificationStatus,
      imageUrl: agent.imageUrl,
      specialization: agent.specialization,
      languages: agent.languages,
      rating: agent.rating,
      experience: agent.experience,
      propertiesSold: agent.propertiesSold,
      credits: agent.credits,
      bio: agent.bio,
      company: agent.company,
      licenseNumber: agent.licenseNumber,
      joinedDate: agent.joinedDate,
    );
  }

  Agent toEntity() {
    return Agent(
      agentId: agentId,
      name: name,
      email: email,
      phone: phone,
      location: location,
      verificationStatus: verificationStatus,
      imageUrl: imageUrl,
      specialization: specialization,
      languages: languages,
      rating: rating,
      experience: experience,
      propertiesSold: propertiesSold,
      credits: credits,
      bio: bio,
      company: company,
      licenseNumber: licenseNumber,
      joinedDate: joinedDate,
    );
  }
}
