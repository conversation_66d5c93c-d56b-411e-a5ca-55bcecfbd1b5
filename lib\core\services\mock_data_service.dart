import '../../features/auth/domain/entities/user.dart';
import '../../features/properties/domain/entities/property.dart';
import '../../features/agents/domain/entities/agent.dart';

class MockDataService {
  static final Map<String, User> _users = {
    "<EMAIL>": User(
      uid: "adminId",
      name: "Admin User",
      email: "<EMAIL>",
      phone: "******-0100",
      location: "Platform HQ",
      userRole: UserRole.admin,
      status: UserStatus.active,
      imageUrl: "https://images.pexels.com/photos/839011/pexels-photo-839011.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ),
    "<EMAIL>": User(
      uid: "userId2",
      name: "Olivia Investor",
      email: "<EMAIL>",
      phone: "******-0102",
      location: "New York, USA",
      userRole: UserRole.investor,
      status: UserStatus.active,
      credits: 20,
      imageUrl: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ),
    "<EMAIL>": User(
      uid: "userId1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "******-0101",
      location: "Dubai, UAE",
      userRole: UserRole.regular,
      status: UserStatus.active,
      imageUrl: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ),
    "<EMAIL>": User(
      uid: "agentId1",
      name: "Pending Agent",
      email: "<EMAIL>",
      phone: "******-0103",
      location: "London, UK",
      userRole: UserRole.agent,
      status: UserStatus.active,
      verificationStatus: VerificationStatus.pending,
      imageUrl: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ),
    "<EMAIL>": User(
      uid: "agentId2",
      name: "Omar Hassan",
      email: "<EMAIL>",
      phone: "+971-55-123-4567",
      location: "Dubai, UAE",
      userRole: UserRole.agent,
      status: UserStatus.active,
      verificationStatus: VerificationStatus.verified,
      credits: 10,
      imageUrl: "https://images.pexels.com/photos/842980/pexels-photo-842980.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ),
  };

  static final List<Property> _properties = [
    Property(
      propertyId: "prop1",
      address: "123 Main St, Anytown, USA",
      type: PropertyType.house,
      status: PropertyStatus.sale,
      price: 350000,
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1500,
      amenities: ["Swimming Pool", "Gym", "Covered Parking"],
      predictedValueGrowth: ValueGrowth.high,
      investmentPotentialScore: 8.5,
      imageUrls: [
        'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
        'https://images.pexels.com/photos/209296/pexels-photo-209296.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
      ],
      latitude: 34.05,
      longitude: -118.24,
      summary: [
        "✅ Priced 3% below market average.",
        "📈 High investment potential.",
        "🏡 Quiet, family-friendly street."
      ],
      verificationStatus: VerificationStatus.verified,
      ownerId: "agentId2",
      isBoosted: true,
      views: 1250,
      inquiries: 45,
      valueForMoneyScore: ValueScore.high,
      demandScore: ValueScore.high,
    ),
    Property(
      propertyId: "prop2",
      address: "456 Oak Ave, Suburbia, Canada",
      type: PropertyType.condo,
      status: PropertyStatus.rent,
      price: 2200,
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1100,
      amenities: ["Gym", "24/7 Security"],
      predictedValueGrowth: ValueGrowth.medium,
      investmentPotentialScore: 7.2,
      imageUrls: [
        'https://images.pexels.com/photos/276724/pexels-photo-276724.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
      ],
      latitude: 45.42,
      longitude: -75.69,
      summary: [
        "✅ Includes gym and pool access.",
        "🚇 10-minute walk to metro station.",
        "⚠️ High service charge reported in area."
      ],
      verificationStatus: VerificationStatus.verified,
      ownerId: "agentId2",
      isBoosted: false,
      views: 800,
      inquiries: 20,
      soldDate: DateTime(2025, 3, 15),
      valueForMoneyScore: ValueScore.medium,
      demandScore: ValueScore.high,
    ),
  ];

  static final List<Agent> _agents = [
    Agent(
      agentId: "agentId2",
      name: "Omar Hassan",
      email: "<EMAIL>",
      phone: "+971-55-123-4567",
      location: "Dubai, UAE",
      verificationStatus: VerificationStatus.verified,
      imageUrl: "https://images.pexels.com/photos/842980/pexels-photo-842980.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      specialization: ["Luxury", "Waterfront"],
      languages: ["Arabic", "English"],
      rating: 4.9,
      experience: 8,
      propertiesSold: 112,
    ),
    Agent(
      agentId: "agentId3",
      name: "Sarah Khan",
      email: "<EMAIL>",
      phone: "+971-50-789-1234",
      location: "Dubai Marina",
      verificationStatus: VerificationStatus.verified,
      imageUrl: "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      specialization: ["Apartments", "High-rise"],
      languages: ["English", "Urdu"],
      rating: 4.8,
      experience: 5,
      propertiesSold: 89,
    ),
  ];

  // Getters for accessing mock data
  Map<String, User> get users => Map.from(_users);
  List<Property> get properties => List.from(_properties);
  List<Agent> get agents => List.from(_agents);

  // Authentication methods
  Future<User?> authenticateUser(String email, String password) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    
    if (password != 'password') return null;
    
    final user = _users[email];
    if (user?.status == UserStatus.suspended) return null;
    
    return user;
  }

  // Property methods
  Future<List<Property>> getProperties({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return _properties.where((property) {
      if (type != null && property.type != type) return false;
      if (status != null && property.status != status) return false;
      if (minPrice != null && property.price < minPrice) return false;
      if (maxPrice != null && property.price > maxPrice) return false;
      if (minBedrooms != null && property.bedrooms < minBedrooms) return false;
      if (maxBedrooms != null && property.bedrooms > maxBedrooms) return false;
      return true;
    }).toList();
  }

  Future<Property?> getPropertyById(String propertyId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    try {
      return _properties.firstWhere((property) => property.propertyId == propertyId);
    } catch (e) {
      return null;
    }
  }

  // Agent methods
  Future<List<Agent>> getAgents() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _agents;
  }

  Future<Agent?> getAgentById(String agentId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    try {
      return _agents.firstWhere((agent) => agent.agentId == agentId);
    } catch (e) {
      return null;
    }
  }
}
