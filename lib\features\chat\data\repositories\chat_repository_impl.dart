import 'package:dartz/dartz.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/error/failures.dart';
import '../../domain/entities/conversation.dart';
import '../../domain/entities/message.dart';
import '../../domain/repositories/chat_repository.dart';
import '../datasources/chat_local_datasource.dart';
import '../models/conversation_model.dart';
import '../models/message_model.dart';

class ChatRepositoryImpl implements ChatRepository {
  final ChatLocalDataSource localDataSource;
  final Uuid uuid = const Uuid();

  ChatRepositoryImpl({
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, List<Conversation>>> getConversations({
    String? userId,
    int? limit,
    int? offset,
  }) async {
    try {
      final conversations = await localDataSource.getConversations(
        userId: userId,
        limit: limit,
        offset: offset,
      );
      return Right(conversations.map((c) => c.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> getConversation(String conversationId) async {
    try {
      final conversation = await localDataSource.getConversation(conversationId);
      return Right(conversation.toEntity());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> createConversation({
    required String title,
    String? userId,
    String? firstMessage,
  }) async {
    try {
      final conversation = Conversation.create(
        id: uuid.v4(),
        title: title,
        userId: userId,
        firstMessage: firstMessage,
      );
      
      final conversationModel = ConversationModel.fromEntity(conversation);
      await localDataSource.saveConversation(conversationModel);
      
      return Right(conversation);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> updateConversation(Conversation conversation) async {
    try {
      final updatedConversation = conversation.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final conversationModel = ConversationModel.fromEntity(updatedConversation);
      await localDataSource.updateConversation(conversationModel);
      
      return Right(updatedConversation);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteConversation(String conversationId) async {
    try {
      await localDataSource.deleteConversation(conversationId);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> pinConversation(String conversationId, bool isPinned) async {
    try {
      final conversation = await localDataSource.getConversation(conversationId);
      final updatedConversation = conversation.toEntity().copyWith(
        isPinned: isPinned,
        updatedAt: DateTime.now(),
      );

      final conversationModel = ConversationModel.fromEntity(updatedConversation);
      await localDataSource.updateConversation(conversationModel);

      return Right(updatedConversation);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> favoriteConversation(String conversationId, bool isFavorite) async {
    try {
      final conversation = await localDataSource.getConversation(conversationId);
      final updatedConversation = conversation.toEntity().copyWith(
        isFavorite: isFavorite,
        updatedAt: DateTime.now(),
      );

      final conversationModel = ConversationModel.fromEntity(updatedConversation);
      await localDataSource.updateConversation(conversationModel);

      return Right(updatedConversation);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Conversation>> renameConversation(String conversationId, String newTitle) async {
    try {
      final conversation = await localDataSource.getConversation(conversationId);
      final updatedConversation = conversation.toEntity().copyWith(
        title: newTitle,
        updatedAt: DateTime.now(),
      );

      final conversationModel = ConversationModel.fromEntity(updatedConversation);
      await localDataSource.updateConversation(conversationModel);

      return Right(updatedConversation);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Message>>> getMessages({
    required String conversationId,
    int? limit,
    int? offset,
  }) async {
    try {
      final messages = await localDataSource.getMessages(
        conversationId: conversationId,
        limit: limit,
        offset: offset,
      );
      return Right(messages.map((m) => m.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Message>> sendMessage({
    required String conversationId,
    required String content,
    required MessageType type,
    Map<String, dynamic>? metadata,
    List<String>? attachments,
  }) async {
    try {
      final message = Message(
        id: uuid.v4(),
        content: content,
        type: type,
        timestamp: DateTime.now(),
        conversationId: conversationId,
        metadata: metadata,
        attachments: attachments,
      );
      
      final messageModel = MessageModel.fromEntity(message);
      await localDataSource.saveMessage(messageModel);
      
      // Update conversation's last message info
      final conversation = await localDataSource.getConversation(conversationId);
      final updatedConversation = conversation.toEntity().copyWith(
        messageCount: conversation.messageCount + 1,
        lastMessagePreview: content.length > 50 ? '${content.substring(0, 50)}...' : content,
        lastMessageAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final conversationModel = ConversationModel.fromEntity(updatedConversation);
      await localDataSource.updateConversation(conversationModel);
      
      return Right(message);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Message>> updateMessage(Message message) async {
    try {
      final updatedMessage = message.copyWith(
        isEdited: true,
        editedAt: DateTime.now(),
      );
      
      final messageModel = MessageModel.fromEntity(updatedMessage);
      await localDataSource.updateMessage(messageModel);
      
      return Right(updatedMessage);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMessage(String messageId) async {
    try {
      await localDataSource.deleteMessage(messageId);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Message>> regenerateMessage(String messageId) async {
    try {
      // For now, return the same message with regenerated status
      // In a real implementation, this would call an AI service
      final message = await localDataSource.getMessage(messageId);
      final regeneratedMessage = message.toEntity().copyWith(
        content: '${message.content} (Regenerated)',
        timestamp: DateTime.now(),
      );
      
      final messageModel = MessageModel.fromEntity(regeneratedMessage);
      await localDataSource.updateMessage(messageModel);
      
      return Right(regeneratedMessage);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Conversation>>> searchConversations({
    required String query,
    String? userId,
    int? limit,
  }) async {
    try {
      final conversations = await localDataSource.searchConversations(
        query: query,
        userId: userId,
        limit: limit,
      );
      return Right(conversations.map((c) => c.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Message>>> searchMessages({
    required String query,
    String? conversationId,
    String? userId,
    int? limit,
  }) async {
    try {
      final messages = await localDataSource.searchMessages(
        query: query,
        conversationId: conversationId,
        userId: userId,
        limit: limit,
      );
      return Right(messages.map((m) => m.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportConversation({
    required String conversationId,
    required String format,
  }) async {
    try {
      final exportData = await localDataSource.exportConversation(
        conversationId: conversationId,
        format: format,
      );
      return Right(exportData);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> shareConversation(String conversationId) async {
    try {
      final shareLink = await localDataSource.shareConversation(conversationId);
      return Right(shareLink);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMultipleConversations(List<String> conversationIds) async {
    try {
      await localDataSource.deleteMultipleConversations(conversationIds);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> archiveConversations(List<String> conversationIds) async {
    try {
      await localDataSource.archiveConversations(conversationIds);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getConversationStats(String? userId) async {
    try {
      final stats = await localDataSource.getConversationStats(userId);
      return Right(stats);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> syncConversations() async {
    try {
      // In a real implementation, this would sync with a remote server
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Conversation>>> getCachedConversations() async {
    try {
      final conversations = await localDataSource.getCachedConversations();
      return Right(conversations.map((c) => c.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }
}
