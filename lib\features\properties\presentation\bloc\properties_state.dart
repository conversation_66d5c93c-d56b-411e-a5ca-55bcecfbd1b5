part of 'properties_bloc.dart';

abstract class PropertiesState extends Equatable {
  const PropertiesState();

  @override
  List<Object?> get props => [];
}

class PropertiesInitial extends PropertiesState {}

class PropertiesLoading extends PropertiesState {}

class PropertyDetailsLoading extends PropertiesState {}

class PropertyActionLoading extends PropertiesState {}

class PropertiesLoaded extends PropertiesState {
  final List<Property> properties;

  const PropertiesLoaded({required this.properties});

  @override
  List<Object> get props => [properties];
}

class PropertyDetailsLoaded extends PropertiesState {
  final Property property;

  const PropertyDetailsLoaded({required this.property});

  @override
  List<Object> get props => [property];
}

class PropertyActionSuccess extends PropertiesState {
  final String message;
  final Property? property;

  const PropertyActionSuccess({
    required this.message,
    this.property,
  });

  @override
  List<Object?> get props => [message, property];
}

class PropertiesError extends PropertiesState {
  final String message;

  const PropertiesError({required this.message});

  @override
  List<Object> get props => [message];
}
