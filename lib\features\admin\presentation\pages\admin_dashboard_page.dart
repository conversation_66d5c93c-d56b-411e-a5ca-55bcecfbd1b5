import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/theme/app_colors.dart';

class AdminDashboardPage extends StatelessWidget {
  const AdminDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Admin Dashboard',
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // System Overview
            Text(
              'System Overview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
              children: [
                _buildMetricCard(
                  context,
                  'Total Users',
                  '1,247',
                  '+23 this week',
                  AppColors.primary,
                  Icons.people,
                ),
                _buildMetricCard(
                  context,
                  'Active Agents',
                  '89',
                  '12 pending',
                  AppColors.success,
                  Icons.verified_user,
                ),
                _buildMetricCard(
                  context,
                  'Properties',
                  '3,456',
                  '45 pending',
                  AppColors.warning,
                  Icons.home_work,
                ),
                _buildMetricCard(
                  context,
                  'Revenue',
                  '\$45.2K',
                  '+8.5% this month',
                  AppColors.info,
                  Icons.attach_money,
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Quick Actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.primaryLight,
                      child: Icon(Icons.people, color: AppColors.primary),
                    ),
                    title: const Text('Manage Users'),
                    subtitle: const Text('View and manage user accounts'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to user management
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.successLight,
                      child: Icon(Icons.verified_user, color: AppColors.success),
                    ),
                    title: const Text('Agent Verification'),
                    subtitle: const Text('Review pending agent applications'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to agent verification
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.warningLight,
                      child: Icon(Icons.home_work, color: AppColors.warning),
                    ),
                    title: const Text('Property Verification'),
                    subtitle: const Text('Review property listings'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to property verification
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.infoLight,
                      child: Icon(Icons.settings, color: AppColors.info),
                    ),
                    title: const Text('Feature Control'),
                    subtitle: const Text('Manage system features'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // Navigate to feature control
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    String subtitle,
    Color color,
    IconData icon,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
