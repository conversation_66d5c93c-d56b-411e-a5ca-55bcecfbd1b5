import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/property_model.dart';

abstract class PropertiesLocalDataSource {
  Future<List<PropertyModel>> getCachedProperties();
  Future<void> cacheProperties(List<PropertyModel> properties);
  Future<PropertyModel?> getCachedProperty(String propertyId);
  Future<void> cacheProperty(PropertyModel property);
  Future<void> clearCachedProperties();
}

class PropertiesLocalDataSourceImpl implements PropertiesLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const String cachedPropertiesKey = 'CACHED_PROPERTIES';
  static const String cachedPropertyPrefix = 'CACHED_PROPERTY_';

  PropertiesLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<PropertyModel>> getCachedProperties() async {
    final jsonString = sharedPreferences.getString(cachedPropertiesKey);
    if (jsonString != null) {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => PropertyModel.fromJson(json)).toList();
    }
    return [];
  }

  @override
  Future<void> cacheProperties(List<PropertyModel> properties) async {
    final jsonList = properties.map((property) => property.toJson()).toList();
    await sharedPreferences.setString(
      cachedPropertiesKey,
      json.encode(jsonList),
    );
  }

  @override
  Future<PropertyModel?> getCachedProperty(String propertyId) async {
    final jsonString = sharedPreferences.getString('$cachedPropertyPrefix$propertyId');
    if (jsonString != null) {
      return PropertyModel.fromJson(json.decode(jsonString));
    }
    return null;
  }

  @override
  Future<void> cacheProperty(PropertyModel property) async {
    await sharedPreferences.setString(
      '$cachedPropertyPrefix${property.propertyId}',
      json.encode(property.toJson()),
    );
  }

  @override
  Future<void> clearCachedProperties() async {
    await sharedPreferences.remove(cachedPropertiesKey);
    
    // Remove individual cached properties
    final keys = sharedPreferences.getKeys();
    for (final key in keys) {
      if (key.startsWith(cachedPropertyPrefix)) {
        await sharedPreferences.remove(key);
      }
    }
  }
}
