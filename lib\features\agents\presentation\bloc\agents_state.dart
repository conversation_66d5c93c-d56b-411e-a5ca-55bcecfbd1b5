part of 'agents_bloc.dart';

abstract class AgentsState extends Equatable {
  const AgentsState();

  @override
  List<Object?> get props => [];
}

class AgentsInitial extends AgentsState {}

class AgentsLoading extends AgentsState {}

class AgentDetailsLoading extends AgentsState {}

class AgentsLoaded extends AgentsState {
  final List<Agent> agents;

  const AgentsLoaded({required this.agents});

  @override
  List<Object> get props => [agents];
}

class AgentDetailsLoaded extends AgentsState {
  final Agent agent;

  const AgentDetailsLoaded({required this.agent});

  @override
  List<Object> get props => [agent];
}

class AgentsError extends AgentsState {
  final String message;

  const AgentsError({required this.message});

  @override
  List<Object> get props => [message];
}
