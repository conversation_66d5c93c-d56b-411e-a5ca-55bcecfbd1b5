import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/entities/property.dart';
import '../../domain/usecases/get_properties.dart';
import '../../domain/usecases/get_property_details.dart';
import '../../domain/usecases/search_properties.dart';
import '../../domain/usecases/add_property.dart';
import '../../domain/usecases/update_property.dart';
import '../../domain/usecases/delete_property.dart';

part 'properties_event.dart';
part 'properties_state.dart';

class PropertiesBloc extends Bloc<PropertiesEvent, PropertiesState> {
  final GetProperties getProperties;
  final GetPropertyDetails getPropertyDetails;
  final SearchProperties searchProperties;
  final AddProperty addProperty;
  final UpdateProperty updateProperty;
  final DeleteProperty deleteProperty;

  PropertiesBloc({
    required this.getProperties,
    required this.getPropertyDetails,
    required this.searchProperties,
    required this.addProperty,
    required this.updateProperty,
    required this.deleteProperty,
  }) : super(PropertiesInitial()) {
    on<PropertiesLoadRequested>(_onPropertiesLoadRequested);
    on<PropertyDetailsLoadRequested>(_onPropertyDetailsLoadRequested);
    on<PropertiesSearchRequested>(_onPropertiesSearchRequested);
    on<PropertyAddRequested>(_onPropertyAddRequested);
    on<PropertyUpdateRequested>(_onPropertyUpdateRequested);
    on<PropertyDeleteRequested>(_onPropertyDeleteRequested);
    on<PropertiesFilterChanged>(_onPropertiesFilterChanged);
  }

  Future<void> _onPropertiesLoadRequested(
    PropertiesLoadRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertiesLoading());
    
    final result = await getProperties(GetPropertiesParams(
      type: event.type,
      status: event.status,
      minPrice: event.minPrice,
      maxPrice: event.maxPrice,
      minBedrooms: event.minBedrooms,
      maxBedrooms: event.maxBedrooms,
    ));
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (properties) => emit(PropertiesLoaded(properties: properties)),
    );
  }

  Future<void> _onPropertyDetailsLoadRequested(
    PropertyDetailsLoadRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertyDetailsLoading());
    
    final result = await getPropertyDetails(
      GetPropertyDetailsParams(propertyId: event.propertyId),
    );
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (property) => emit(PropertyDetailsLoaded(property: property)),
    );
  }

  Future<void> _onPropertiesSearchRequested(
    PropertiesSearchRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertiesLoading());
    
    final result = await searchProperties(
      SearchPropertiesParams(query: event.query),
    );
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (properties) => emit(PropertiesLoaded(properties: properties)),
    );
  }

  Future<void> _onPropertyAddRequested(
    PropertyAddRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertyActionLoading());
    
    final result = await addProperty(
      AddPropertyParams(property: event.property),
    );
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (property) => emit(PropertyActionSuccess(
        message: 'Property added successfully',
        property: property,
      )),
    );
  }

  Future<void> _onPropertyUpdateRequested(
    PropertyUpdateRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertyActionLoading());
    
    final result = await updateProperty(
      UpdatePropertyParams(property: event.property),
    );
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (property) => emit(PropertyActionSuccess(
        message: 'Property updated successfully',
        property: property,
      )),
    );
  }

  Future<void> _onPropertyDeleteRequested(
    PropertyDeleteRequested event,
    Emitter<PropertiesState> emit,
  ) async {
    emit(PropertyActionLoading());
    
    final result = await deleteProperty(
      DeletePropertyParams(propertyId: event.propertyId),
    );
    
    result.fold(
      (failure) => emit(PropertiesError(message: failure.message)),
      (_) => emit(const PropertyActionSuccess(
        message: 'Property deleted successfully',
      )),
    );
  }

  Future<void> _onPropertiesFilterChanged(
    PropertiesFilterChanged event,
    Emitter<PropertiesState> emit,
  ) async {
    // Re-load properties with new filters
    add(PropertiesLoadRequested(
      type: event.type,
      status: event.status,
      minPrice: event.minPrice,
      maxPrice: event.maxPrice,
      minBedrooms: event.minBedrooms,
      maxBedrooms: event.maxBedrooms,
    ));
  }
}
