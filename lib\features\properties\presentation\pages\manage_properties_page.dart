import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class ManagePropertiesPage extends StatelessWidget {
  const ManagePropertiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Manage Properties',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'No Properties Yet',
          message: 'Start by adding your first property listing.',
          icon: Icons.add_home_outlined,
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Add property
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
