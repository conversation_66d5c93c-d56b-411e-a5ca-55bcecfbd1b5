import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';

class AppDrawer extends StatelessWidget {
  final User user;

  const AppDrawer({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 256, // w-64 from HTML
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          right: BorderSide(
            color: const Color(0xFFE2E8F0), // border-slate-200 from HTML
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Header - Matching HTML design
          _buildHeader(context),

          // Navigation Items
          Expanded(
            child: <PERSON>View(
              padding: const EdgeInsets.all(16), // p-4 from HTML
              children: _buildNavigationItems(context),
            ),
          ),

          // Footer
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.home_work_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'PROPIMATCH',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w800,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const Spacer(),
          
          // User Info
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: CachedNetworkImageProvider(user.imageUrl),
                backgroundColor: Colors.white.withValues(alpha: 0.2),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      user.roleDisplayName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _buildNavigationItems(BuildContext context) {
    final items = <Widget>[];

    // Get navigation items based on user role
    final navItems = _getNavigationItemsForRole(user.userRole, user.verificationStatus);

    for (final item in navItems) {
      final isActive = _isCurrentRoute(context, item['route'] as String);

      items.add(
        Container(
          margin: const EdgeInsets.only(bottom: 4), // space-y-1 from HTML
          child: Material(
            color: isActive ? AppColors.navItemActiveBackground : Colors.transparent,
            borderRadius: BorderRadius.circular(8), // rounded-lg from HTML
            child: InkWell(
              onTap: () {
                Navigator.of(context).pop(); // Close drawer
                context.go(item['route'] as String);
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // px-3 py-2 from HTML
                child: Row(
                  children: [
                    Icon(
                      item['icon'] as IconData,
                      size: 20, // w-5 h-5 from HTML
                      color: isActive ? AppColors.navItemActive : AppColors.navItemDefault,
                    ),
                    const SizedBox(width: 12), // ml-3 from HTML
                    Text(
                      item['label'] as String,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: isActive ? AppColors.navItemActive : AppColors.navItemDefault,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }

    return items;
  }

  bool _isCurrentRoute(BuildContext context, String route) {
    final currentRoute = GoRouterState.of(context).uri.path;
    return currentRoute == route;
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: ListTile(
        leading: Icon(
          Icons.logout,
          color: AppColors.error,
        ),
        title: Text(
          'Sign Out',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.error,
            fontWeight: FontWeight.w500,
          ),
        ),
        onTap: () {
          Navigator.of(context).pop(); // Close drawer
          context.read<AuthBloc>().add(const AuthLogoutRequested());
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getNavigationItemsForRole(
    UserRole role, 
    VerificationStatus? verificationStatus,
  ) {
    switch (role) {
      case UserRole.admin:
        return [
          {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/admin'},
          {'icon': Icons.people, 'label': 'Users', 'route': '/admin/users'},
          {'icon': Icons.settings, 'label': 'Features', 'route': '/admin/features'},
          {'icon': Icons.verified_user, 'label': 'Agent Verification', 'route': '/admin/agent-verification'},
          {'icon': Icons.home_work, 'label': 'Property Verification', 'route': '/admin/property-verification'},
        ];
      
      case UserRole.agent:
        if (verificationStatus == VerificationStatus.verified) {
          return [
            {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/agent-dashboard'},
            {'icon': Icons.assignment, 'label': 'My Leads', 'route': '/agent-leads'},
            {'icon': Icons.search, 'label': 'Property Finder', 'route': '/property-finder'},
            {'icon': Icons.home_work, 'label': 'My Listings', 'route': '/manage-properties'},
            {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
            {'icon': Icons.notifications, 'label': 'Notifications', 'route': '/notifications'},
          ];
        } else {
          return [
            {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/dashboard'},
            {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
          ];
        }
      
      case UserRole.investor:
        return [
          {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/investor-dashboard'},
          {'icon': Icons.home_work, 'label': 'My Properties', 'route': '/manage-properties'},
          {'icon': Icons.notifications, 'label': 'Notifications', 'route': '/notifications'},
          {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
        ];
      
      case UserRole.regular:
        return [
          {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/dashboard'},
          {'icon': Icons.search, 'label': 'Property Finder', 'route': '/property-finder'},
          {'icon': Icons.people, 'label': 'Find Agents', 'route': '/agents'},
          {'icon': Icons.notifications, 'label': 'Notifications', 'route': '/notifications'},
          {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
        ];
    }
  }
}
