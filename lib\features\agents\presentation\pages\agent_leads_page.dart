import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class AgentLeadsPage extends StatelessWidget {
  const AgentLeadsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Leads',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'No Leads Yet',
          message: 'New leads will appear here when clients show interest in your properties.',
          icon: Icons.assignment_outlined,
        ),
      ),
    );
  }
}
