import 'package:flutter/material.dart';

class AppColors {
  // Brand Colors - Based on PROPIMATCH orange theme
  static const Color primary = Color(0xFFDD6B20);
  static const Color primaryDark = Color(0xFFC05621);
  static const Color primaryLight = Color(0xFFFFF5EB);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF312E81);
  static const Color secondaryLight = Color(0xFFF1F5F9);
  
  // Neutral Colors
  static const Color background = Color(0xFFF8FAFC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF1F5F9);
  
  // Text Colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF1E293B);
  static const Color onSurface = Color(0xFF334155);
  static const Color onSurfaceVariant = Color(0xFF64748B);
  
  // Status Colors
  static const Color success = Color(0xFF10B981);
  static const Color successLight = Color(0xFFECFDF5);
  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFEF3C7);
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFFEF2F2);
  static const Color info = Color(0xFF3B82F6);
  static const Color infoLight = Color(0xFFEFF6FF);
  
  // Border and Outline
  static const Color outline = Color(0xFFE2E8F0);
  static const Color outlineVariant = Color(0xFFF1F5F9);
  
  // Investment Potential Colors
  static const Color highPotential = Color(0xFF10B981);
  static const Color mediumPotential = Color(0xFFF59E0B);
  static const Color lowPotential = Color(0xFFEF4444);
  
  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFFDD6B20),
    Color(0xFF10B981),
    Color(0xFF3B82F6),
    Color(0xFFF59E0B),
    Color(0xFF8B5CF6),
    Color(0xFFEF4444),
    Color(0xFF06B6D4),
    Color(0xFFEC4899),
  ];
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFFF97316)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFF059669)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, Color(0xFFD97706)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, Color(0xFFDC2626)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Gemini Button Gradient - Matching HTML .gemini-btn
  static const LinearGradient geminiGradient = LinearGradient(
    colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)], // indigo-600 to purple-600
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadow Colors - Matching HTML design
  static Color get shadowLight => Colors.black.withValues(alpha: 0.07);
  static Color get shadowMedium => Colors.black.withValues(alpha: 0.1);
  static Color get shadowDark => Colors.black.withValues(alpha: 0.15);

  // Card Shadow System - Matching HTML .card-shadow
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.07),
      blurRadius: 6,
      offset: const Offset(0, 4),
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.07),
      blurRadius: 4,
      offset: const Offset(0, 2),
      spreadRadius: -2,
    ),
  ];

  // Card Hover Shadow - Matching HTML .card-shadow:hover
  static List<BoxShadow> get cardShadowHover => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 15,
      offset: const Offset(0, 10),
      spreadRadius: -3,
    ),
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 6,
      offset: const Offset(0, 4),
      spreadRadius: -4,
    ),
  ];

  // Boosted Card Shadow - Matching HTML .boosted-card
  static List<BoxShadow> get boostedCardShadow => [
    BoxShadow(
      color: primary.withValues(alpha: 0.3),
      blurRadius: 15,
      offset: const Offset(0, 0),
      spreadRadius: 0,
    ),
  ];

  // Add missing onError color
  static const Color onError = Color(0xFFFFFFFF);

  // Navigation Colors - Matching HTML design
  static const Color navItemDefault = Color(0xFF64748B);
  static const Color navItemHover = Color(0xFF312E81);
  static const Color navItemActiveBackground = Color(0xFFFFF5EB);
  static const Color navItemActive = Color(0xFFC05621);

  // Input Focus Colors - Matching HTML design
  static const Color inputFocusRing = Color(0xFFDD6B20);
  static const Color inputBorder = Color(0xFFCBD5E1);
  
  // Property Type Colors
  static const Map<String, Color> propertyTypeColors = {
    'House': Color(0xFF10B981),
    'Condo': Color(0xFF3B82F6),
    'Townhouse': Color(0xFFF59E0B),
    'Commercial': Color(0xFF8B5CF6),
    'Apartment': Color(0xFFEC4899),
    'Villa': Color(0xFF06B6D4),
  };
  
  // Status Colors for Properties
  static const Map<String, Color> propertyStatusColors = {
    'sale': Color(0xFF10B981),
    'rent': Color(0xFF3B82F6),
    'sold': Color(0xFF64748B),
    'rented': Color(0xFF64748B),
  };
  
  // User Role Colors
  static const Map<String, Color> userRoleColors = {
    'admin': Color(0xFF8B5CF6),
    'agent': Color(0xFFDD6B20),
    'investor': Color(0xFF10B981),
    'regular': Color(0xFF3B82F6),
  };
  
  // Verification Status Colors
  static const Map<String, Color> verificationStatusColors = {
    'Verified': Color(0xFF10B981),
    'Pending': Color(0xFFF59E0B),
    'Rejected': Color(0xFFEF4444),
  };
}
