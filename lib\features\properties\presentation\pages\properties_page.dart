import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';

class PropertiesPage extends StatelessWidget {
  const PropertiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Properties',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'Properties Coming Soon',
          message: 'Property listings will be available here.',
          icon: Icons.home_work_outlined,
        ),
      ),
    );
  }
}
