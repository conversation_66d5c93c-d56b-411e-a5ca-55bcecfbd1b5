import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/conversation.dart';
import '../repositories/chat_repository.dart';

class GetConversations implements UseCase<List<Conversation>, GetConversationsParams> {
  final ChatRepository repository;

  GetConversations(this.repository);

  @override
  Future<Either<Failure, List<Conversation>>> call(GetConversationsParams params) async {
    return await repository.getConversations(
      userId: params.userId,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

class GetConversationsParams extends Equatable {
  final String? userId;
  final int? limit;
  final int? offset;

  const GetConversationsParams({
    this.userId,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [userId, limit, offset];
}
