import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/agent.dart';
import '../../domain/usecases/get_agents.dart';
import '../../domain/usecases/get_agent_details.dart';

part 'agents_event.dart';
part 'agents_state.dart';

class AgentsBloc extends Bloc<AgentsEvent, AgentsState> {
  final GetAgents getAgents;
  final GetAgentDetails getAgentDetails;

  AgentsBloc({
    required this.getAgents,
    required this.getAgentDetails,
  }) : super(AgentsInitial()) {
    on<AgentsLoadRequested>(_onAgentsLoadRequested);
    on<AgentDetailsLoadRequested>(_onAgentDetailsLoadRequested);
  }

  Future<void> _onAgentsLoadRequested(
    AgentsLoadRequested event,
    Emitter<AgentsState> emit,
  ) async {
    emit(AgentsLoading());
    
    final result = await getAgents(const NoParams());
    
    result.fold(
      (failure) => emit(AgentsError(message: failure.message)),
      (agents) => emit(AgentsLoaded(agents: agents)),
    );
  }

  Future<void> _onAgentDetailsLoadRequested(
    AgentDetailsLoadRequested event,
    Emitter<AgentsState> emit,
  ) async {
    emit(AgentDetailsLoading());
    
    final result = await getAgentDetails(
      GetAgentDetailsParams(agentId: event.agentId),
    );
    
    result.fold(
      (failure) => emit(AgentsError(message: failure.message)),
      (agent) => emit(AgentDetailsLoaded(agent: agent)),
    );
  }
}
