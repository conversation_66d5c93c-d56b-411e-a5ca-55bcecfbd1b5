import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class FeatureControlPage extends StatelessWidget {
  const FeatureControlPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Feature Control',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'Feature Control',
          message: 'Feature management controls will be available here.',
          icon: Icons.settings_outlined,
        ),
      ),
    );
  }
}
