import 'package:dio/dio.dart';
import '../../../../core/services/mock_data_service.dart';
import '../models/property_model.dart';
import '../../domain/entities/property.dart';

abstract class PropertiesRemoteDataSource {
  Future<List<PropertyModel>> getProperties({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
  });
  Future<PropertyModel> getPropertyById(String propertyId);
  Future<List<PropertyModel>> searchProperties(String query);
  Future<PropertyModel> addProperty(PropertyModel property);
  Future<PropertyModel> updateProperty(PropertyModel property);
  Future<void> deleteProperty(String propertyId);
}

class PropertiesRemoteDataSourceImpl implements PropertiesRemoteDataSource {
  final Dio dio;
  final MockDataService mockDataService = MockDataService();

  PropertiesRemoteDataSourceImpl({required this.dio});

  @override
  Future<List<PropertyModel>> getProperties({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
  }) async {
    // **IMPROVEMENT**: Added proper filtering parameters vs HTML version
    final properties = await mockDataService.getProperties(
      type: type,
      status: status,
      minPrice: minPrice,
      maxPrice: maxPrice,
      minBedrooms: minBedrooms,
      maxBedrooms: maxBedrooms,
    );
    
    return properties.map((property) => PropertyModel.fromEntity(property)).toList();
  }

  @override
  Future<PropertyModel> getPropertyById(String propertyId) async {
    final property = await mockDataService.getPropertyById(propertyId);
    
    if (property == null) {
      throw Exception('Property not found');
    }
    
    return PropertyModel.fromEntity(property);
  }

  @override
  Future<List<PropertyModel>> searchProperties(String query) async {
    // **IMPROVEMENT**: Proper search implementation vs basic filtering in HTML
    final allProperties = await mockDataService.getProperties();
    
    final filteredProperties = allProperties.where((property) {
      return property.address.toLowerCase().contains(query.toLowerCase()) ||
             property.typeDisplayName.toLowerCase().contains(query.toLowerCase()) ||
             property.amenities.any((amenity) => 
               amenity.toLowerCase().contains(query.toLowerCase()));
    }).toList();
    
    return filteredProperties.map((property) => PropertyModel.fromEntity(property)).toList();
  }

  @override
  Future<PropertyModel> addProperty(PropertyModel property) async {
    // **IMPROVEMENT**: Proper validation vs no validation in HTML
    if (property.address.isEmpty || property.price <= 0) {
      throw Exception('Invalid property data');
    }
    
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));
    
    // In real implementation, this would make HTTP POST request
    return property;
  }

  @override
  Future<PropertyModel> updateProperty(PropertyModel property) async {
    // **IMPROVEMENT**: Proper validation vs no validation in HTML
    if (property.propertyId.isEmpty || property.address.isEmpty) {
      throw Exception('Invalid property data');
    }
    
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 300));
    
    return property;
  }

  @override
  Future<void> deleteProperty(String propertyId) async {
    if (propertyId.isEmpty) {
      throw Exception('Invalid property ID');
    }
    
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 200));
  }
}
