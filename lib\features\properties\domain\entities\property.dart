import 'package:equatable/equatable.dart';
import '../../../auth/domain/entities/user.dart';

enum PropertyType {
  house,
  condo,
  townhouse,
  commercial,
  apartment,
  villa,
}

enum PropertyStatus {
  sale,
  rent,
  sold,
  rented,
}

enum ValueGrowth {
  high,
  medium,
  low,
}

enum ValueScore {
  high,
  medium,
  low,
}

class Property extends Equatable {
  final String propertyId;
  final String address;
  final PropertyType type;
  final PropertyStatus status;
  final double price;
  final int bedrooms;
  final int bathrooms;
  final int sqft;
  final List<String> amenities;
  final ValueGrowth predictedValueGrowth;
  final double investmentPotentialScore;
  final List<String> imageUrls;
  final double latitude;
  final double longitude;
  final List<String> summary;
  final VerificationStatus verificationStatus;
  final String? videoUrl;
  final String ownerId;
  final bool isBoosted;
  final int views;
  final int inquiries;
  final DateTime? soldDate;
  final ValueScore valueForMoneyScore;
  final ValueScore demandScore;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Property({
    required this.propertyId,
    required this.address,
    required this.type,
    required this.status,
    required this.price,
    required this.bedrooms,
    required this.bathrooms,
    required this.sqft,
    required this.amenities,
    required this.predictedValueGrowth,
    required this.investmentPotentialScore,
    required this.imageUrls,
    required this.latitude,
    required this.longitude,
    required this.summary,
    required this.verificationStatus,
    required this.ownerId,
    required this.isBoosted,
    required this.views,
    required this.inquiries,
    required this.valueForMoneyScore,
    required this.demandScore,
    this.videoUrl,
    this.soldDate,
    this.createdAt,
    this.updatedAt,
  });

  Property copyWith({
    String? propertyId,
    String? address,
    PropertyType? type,
    PropertyStatus? status,
    double? price,
    int? bedrooms,
    int? bathrooms,
    int? sqft,
    List<String>? amenities,
    ValueGrowth? predictedValueGrowth,
    double? investmentPotentialScore,
    List<String>? imageUrls,
    double? latitude,
    double? longitude,
    List<String>? summary,
    VerificationStatus? verificationStatus,
    String? videoUrl,
    String? ownerId,
    bool? isBoosted,
    int? views,
    int? inquiries,
    DateTime? soldDate,
    ValueScore? valueForMoneyScore,
    ValueScore? demandScore,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Property(
      propertyId: propertyId ?? this.propertyId,
      address: address ?? this.address,
      type: type ?? this.type,
      status: status ?? this.status,
      price: price ?? this.price,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      sqft: sqft ?? this.sqft,
      amenities: amenities ?? this.amenities,
      predictedValueGrowth: predictedValueGrowth ?? this.predictedValueGrowth,
      investmentPotentialScore: investmentPotentialScore ?? this.investmentPotentialScore,
      imageUrls: imageUrls ?? this.imageUrls,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      summary: summary ?? this.summary,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      videoUrl: videoUrl ?? this.videoUrl,
      ownerId: ownerId ?? this.ownerId,
      isBoosted: isBoosted ?? this.isBoosted,
      views: views ?? this.views,
      inquiries: inquiries ?? this.inquiries,
      soldDate: soldDate ?? this.soldDate,
      valueForMoneyScore: valueForMoneyScore ?? this.valueForMoneyScore,
      demandScore: demandScore ?? this.demandScore,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        propertyId,
        address,
        type,
        status,
        price,
        bedrooms,
        bathrooms,
        sqft,
        amenities,
        predictedValueGrowth,
        investmentPotentialScore,
        imageUrls,
        latitude,
        longitude,
        summary,
        verificationStatus,
        videoUrl,
        ownerId,
        isBoosted,
        views,
        inquiries,
        soldDate,
        valueForMoneyScore,
        demandScore,
        createdAt,
        updatedAt,
      ];

  // Helper methods
  bool get isForSale => status == PropertyStatus.sale;
  bool get isForRent => status == PropertyStatus.rent;
  bool get isSold => status == PropertyStatus.sold || status == PropertyStatus.rented;
  bool get isVerified => verificationStatus == VerificationStatus.verified;
  bool get isPending => verificationStatus == VerificationStatus.pending;
  
  String get typeDisplayName {
    switch (type) {
      case PropertyType.house:
        return 'House';
      case PropertyType.condo:
        return 'Condo';
      case PropertyType.townhouse:
        return 'Townhouse';
      case PropertyType.commercial:
        return 'Commercial';
      case PropertyType.apartment:
        return 'Apartment';
      case PropertyType.villa:
        return 'Villa';
    }
  }
  
  String get statusDisplayName {
    switch (status) {
      case PropertyStatus.sale:
        return 'For Sale';
      case PropertyStatus.rent:
        return 'For Rent';
      case PropertyStatus.sold:
        return 'Sold';
      case PropertyStatus.rented:
        return 'Rented';
    }
  }
  
  String get priceDisplayText {
    if (isForRent) {
      return '\$${price.toStringAsFixed(0)}/month';
    } else {
      return '\$${(price / 1000).toStringAsFixed(0)}K';
    }
  }
  
  String get fullPriceDisplayText {
    if (isForRent) {
      return '\$${price.toStringAsFixed(0)} per month';
    } else {
      return '\$${price.toStringAsFixed(0)}';
    }
  }
  
  String get bedroomBathroomText {
    return '$bedrooms bed • $bathrooms bath';
  }
  
  String get sqftText {
    return '${sqft.toString()} sqft';
  }
  
  String get valueGrowthDisplayName {
    switch (predictedValueGrowth) {
      case ValueGrowth.high:
        return 'High Growth';
      case ValueGrowth.medium:
        return 'Medium Growth';
      case ValueGrowth.low:
        return 'Low Growth';
    }
  }
  
  String get valueScoreDisplayName {
    switch (valueForMoneyScore) {
      case ValueScore.high:
        return 'Excellent Value';
      case ValueScore.medium:
        return 'Good Value';
      case ValueScore.low:
        return 'Fair Value';
    }
  }
  
  String get demandScoreDisplayName {
    switch (demandScore) {
      case ValueScore.high:
        return 'High Demand';
      case ValueScore.medium:
        return 'Medium Demand';
      case ValueScore.low:
        return 'Low Demand';
    }
  }
}
