import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class PropertyVerificationPage extends StatelessWidget {
  const PropertyVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Property Verification',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'Property Verification',
          message: 'Property verification tools will be available here.',
          icon: Icons.home_work_outlined,
        ),
      ),
    );
  }
}
