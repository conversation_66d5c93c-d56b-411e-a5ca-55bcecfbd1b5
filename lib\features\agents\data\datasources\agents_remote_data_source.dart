import 'package:dio/dio.dart';
import '../../../../core/services/mock_data_service.dart';
import '../models/agent_model.dart';

abstract class AgentsRemoteDataSource {
  Future<List<AgentModel>> getAgents();
  Future<AgentModel> getAgentById(String agentId);
}

class AgentsRemoteDataSourceImpl implements AgentsRemoteDataSource {
  final Dio dio;
  final MockDataService mockDataService = MockDataService();

  AgentsRemoteDataSourceImpl({required this.dio});

  @override
  Future<List<AgentModel>> getAgents() async {
    final agents = await mockDataService.getAgents();
    return agents.map((agent) => AgentModel.fromEntity(agent)).toList();
  }

  @override
  Future<AgentModel> getAgentById(String agentId) async {
    final agent = await mockDataService.getAgentById(agentId);
    
    if (agent == null) {
      throw Exception('Agent not found');
    }
    
    return AgentModel.fromEntity(agent);
  }
}
