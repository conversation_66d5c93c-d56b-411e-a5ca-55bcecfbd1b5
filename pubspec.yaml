name: propimatch
description: "PROPIMATCH AI - Professional Real Estate Platform"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  get_it: ^7.7.0

  # Navigation
  go_router: ^12.1.3

  # UI Components
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.1.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Charts and Data Visualization
  fl_chart: ^0.65.0

  # Maps
  google_maps_flutter: ^2.10.1
  geolocator: ^10.1.1
  geocoding: ^2.2.2
  
  # HTTP and Data
  dio: ^5.3.2
  json_annotation: ^4.8.1
  
  # Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Image Handling
  image_picker: ^1.0.4
  file_picker: ^10.2.0

  # Utils
  intl: ^0.18.1
  uuid: ^4.1.0
  url_launcher: ^6.3.1
  dartz: ^0.10.1
  
  # Animations
  animations: ^2.0.8
  flutter_staggered_animations: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
  
  # Linting
  flutter_lints: ^5.0.0

  # Testing
  bloc_test: ^10.0.0
  mocktail: ^1.0.1

flutter:
  uses-material-design: true

  # Temporarily commented out assets until directories are created
  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/animations/
  #   - assets/data/

  # Temporarily commented out fonts until files are added
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Inter-ExtraBold.ttf
  #         weight: 800
