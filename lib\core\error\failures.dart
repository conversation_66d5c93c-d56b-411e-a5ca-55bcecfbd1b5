import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure(super.message);
}

class CacheFailure extends Failure {
  const CacheFailure(super.message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(super.message);
}

// Auth failures
class AuthFailure extends Failure {
  const AuthFailure(super.message);
}

class InvalidCredentialsFailure extends AuthFailure {
  const InvalidCredentialsFailure() : super('Invalid email or password');
}

class UserSuspendedFailure extends AuthFailure {
  const UserSuspendedFailure() : super('This account has been suspended');
}

class UserNotFoundFailure extends AuthFailure {
  const UserNotFoundFailure() : super('User not found');
}

// Property failures
class PropertyFailure extends Failure {
  const PropertyFailure(super.message);
}

class PropertyNotFoundFailure extends PropertyFailure {
  const PropertyNotFoundFailure() : super('Property not found');
}

// Agent failures
class AgentFailure extends Failure {
  const AgentFailure(super.message);
}

class AgentNotFoundFailure extends AgentFailure {
  const AgentNotFoundFailure() : super('Agent not found');
}
