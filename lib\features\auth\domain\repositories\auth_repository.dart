import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> loginUser(String email, String password);
  Future<Either<Failure, void>> logoutUser();
  Future<Either<Failure, User?>> getCurrentUser();
  Future<Either<Failure, void>> saveUserSession(User user);
  Future<Either<Failure, void>> clearUserSession();
}
