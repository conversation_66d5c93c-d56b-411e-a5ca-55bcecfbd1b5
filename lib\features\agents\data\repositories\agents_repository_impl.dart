import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/agent.dart';
import '../../domain/repositories/agents_repository.dart';
import '../datasources/agents_local_data_source.dart';
import '../datasources/agents_remote_data_source.dart';

class AgentsRepositoryImpl implements AgentsRepository {
  final AgentsRemoteDataSource remoteDataSource;
  final AgentsLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  AgentsRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Agent>>> getAgents() async {
    if (await networkInfo.isConnected) {
      try {
        final agentModels = await remoteDataSource.getAgents();
        await localDataSource.cacheAgents(agentModels);
        return Right(agentModels.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      try {
        final cachedAgents = await localDataSource.getCachedAgents();
        return Right(cachedAgents.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(CacheFailure(e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, Agent>> getAgentDetails(String agentId) async {
    if (await networkInfo.isConnected) {
      try {
        final agentModel = await remoteDataSource.getAgentById(agentId);
        await localDataSource.cacheAgent(agentModel);
        return Right(agentModel.toEntity());
      } catch (e) {
        if (e.toString().contains('not found')) {
          return const Left(AgentNotFoundFailure());
        }
        return Left(ServerFailure(e.toString()));
      }
    } else {
      try {
        final cachedAgent = await localDataSource.getCachedAgent(agentId);
        if (cachedAgent != null) {
          return Right(cachedAgent.toEntity());
        } else {
          return const Left(AgentNotFoundFailure());
        }
      } catch (e) {
        return Left(CacheFailure(e.toString()));
      }
    }
  }
}
