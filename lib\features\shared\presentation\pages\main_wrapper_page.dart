import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/domain/entities/user.dart';
import '../widgets/app_drawer.dart';
import '../widgets/bottom_navigation.dart';

class MainWrapperPage extends StatelessWidget {
  final Widget child;

  const MainWrapperPage({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          // If not authenticated, redirect to login
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.go('/login');
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final user = state.user;
        final showBottomNav = _shouldShowBottomNavigation(context);
        final showDrawer = _shouldShowDrawer(context);

        return Scaffold(
          backgroundColor: AppColors.background,
          drawer: showDrawer ? AppDrawer(user: user) : null,
          body: child,
          bottomNavigationBar: showBottomNav 
              ? BottomNavigation(user: user) 
              : null,
        );
      },
    );
  }

  bool _shouldShowBottomNavigation(BuildContext context) {
    // Show bottom navigation on mobile devices
    return MediaQuery.of(context).size.width < 768;
  }

  bool _shouldShowDrawer(BuildContext context) {
    // Show drawer on larger screens
    return MediaQuery.of(context).size.width >= 768;
  }
}
