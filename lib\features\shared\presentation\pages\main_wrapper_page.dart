import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/widgets/adaptive_navigation.dart';
import '../../../../core/responsive/responsive_layout.dart';

class MainWrapperPage extends StatelessWidget {
  final Widget child;

  const MainWrapperPage({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final currentRoute = GoRouterState.of(context).uri.path;

    return AdaptiveNavigation(
      currentRoute: currentRoute,
      child: Container(
        padding: context.responsivePadding,
        child: child,
      ),
    );
  }
}
