import '../../domain/entities/property.dart';
import '../../../auth/domain/entities/user.dart';

// part 'property_model.g.dart'; // Uncomment after running build_runner

// @JsonSerializable() // Uncomment after running build_runner
class PropertyModel extends Property {
  const PropertyModel({
    required super.propertyId,
    required super.address,
    required super.type,
    required super.status,
    required super.price,
    required super.bedrooms,
    required super.bathrooms,
    required super.sqft,
    required super.amenities,
    required super.predictedValueGrowth,
    required super.investmentPotentialScore,
    required super.imageUrls,
    required super.latitude,
    required super.longitude,
    required super.summary,
    required super.verificationStatus,
    required super.ownerId,
    required super.isBoosted,
    required super.views,
    required super.inquiries,
    required super.valueForMoneyScore,
    required super.demandScore,
    super.videoUrl,
    super.soldDate,
    super.createdAt,
    super.updatedAt,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) {
    return PropertyModel(
      propertyId: json['propertyId'] as String,
      address: json['address'] as String,
      type: PropertyType.values.firstWhere((e) => e.toString() == 'PropertyType.${json['type']}'),
      status: PropertyStatus.values.firstWhere((e) => e.toString() == 'PropertyStatus.${json['status']}'),
      price: (json['price'] as num).toDouble(),
      bedrooms: json['bedrooms'] as int,
      bathrooms: json['bathrooms'] as int,
      sqft: json['sqft'] as int,
      amenities: (json['amenities'] as List<dynamic>).cast<String>(),
      predictedValueGrowth: ValueGrowth.values.firstWhere((e) => e.toString() == 'ValueGrowth.${json['predictedValueGrowth']}'),
      investmentPotentialScore: (json['investmentPotentialScore'] as num).toDouble(),
      imageUrls: (json['imageUrls'] as List<dynamic>).cast<String>(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      summary: (json['summary'] as List<dynamic>).cast<String>(),
      verificationStatus: VerificationStatus.values.firstWhere((e) => e.toString() == 'VerificationStatus.${json['verificationStatus']}'),
      ownerId: json['ownerId'] as String,
      isBoosted: json['isBoosted'] as bool,
      views: json['views'] as int,
      inquiries: json['inquiries'] as int,
      valueForMoneyScore: ValueScore.values.firstWhere((e) => e.toString() == 'ValueScore.${json['valueForMoneyScore']}'),
      demandScore: ValueScore.values.firstWhere((e) => e.toString() == 'ValueScore.${json['demandScore']}'),
      videoUrl: json['videoUrl'] as String?,
      soldDate: json['soldDate'] != null ? DateTime.parse(json['soldDate'] as String) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'propertyId': propertyId,
      'address': address,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'price': price,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'sqft': sqft,
      'amenities': amenities,
      'predictedValueGrowth': predictedValueGrowth.toString().split('.').last,
      'investmentPotentialScore': investmentPotentialScore,
      'imageUrls': imageUrls,
      'latitude': latitude,
      'longitude': longitude,
      'summary': summary,
      'verificationStatus': verificationStatus.toString().split('.').last,
      'videoUrl': videoUrl,
      'ownerId': ownerId,
      'isBoosted': isBoosted,
      'views': views,
      'inquiries': inquiries,
      'soldDate': soldDate?.toIso8601String(),
      'valueForMoneyScore': valueForMoneyScore.toString().split('.').last,
      'demandScore': demandScore.toString().split('.').last,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory PropertyModel.fromEntity(Property property) {
    return PropertyModel(
      propertyId: property.propertyId,
      address: property.address,
      type: property.type,
      status: property.status,
      price: property.price,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      sqft: property.sqft,
      amenities: property.amenities,
      predictedValueGrowth: property.predictedValueGrowth,
      investmentPotentialScore: property.investmentPotentialScore,
      imageUrls: property.imageUrls,
      latitude: property.latitude,
      longitude: property.longitude,
      summary: property.summary,
      verificationStatus: property.verificationStatus,
      videoUrl: property.videoUrl,
      ownerId: property.ownerId,
      isBoosted: property.isBoosted,
      views: property.views,
      inquiries: property.inquiries,
      soldDate: property.soldDate,
      valueForMoneyScore: property.valueForMoneyScore,
      demandScore: property.demandScore,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
    );
  }

  Property toEntity() {
    return Property(
      propertyId: propertyId,
      address: address,
      type: type,
      status: status,
      price: price,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      sqft: sqft,
      amenities: amenities,
      predictedValueGrowth: predictedValueGrowth,
      investmentPotentialScore: investmentPotentialScore,
      imageUrls: imageUrls,
      latitude: latitude,
      longitude: longitude,
      summary: summary,
      verificationStatus: verificationStatus,
      videoUrl: videoUrl,
      ownerId: ownerId,
      isBoosted: isBoosted,
      views: views,
      inquiries: inquiries,
      soldDate: soldDate,
      valueForMoneyScore: valueForMoneyScore,
      demandScore: demandScore,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
