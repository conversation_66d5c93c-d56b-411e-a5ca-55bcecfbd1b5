import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/property.dart';
import '../repositories/properties_repository.dart';

class AddProperty implements UseCase<Property, AddPropertyParams> {
  final PropertiesRepository repository;

  AddProperty(this.repository);

  @override
  Future<Either<Failure, Property>> call(AddPropertyParams params) async {
    return await repository.addProperty(params.property);
  }
}

class AddPropertyParams extends Equatable {
  final Property property;

  const AddPropertyParams({required this.property});

  @override
  List<Object> get props => [property];
}
