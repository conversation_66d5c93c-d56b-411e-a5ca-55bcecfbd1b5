import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/agent_model.dart';

abstract class AgentsLocalDataSource {
  Future<List<AgentModel>> getCachedAgents();
  Future<void> cacheAgents(List<AgentModel> agents);
  Future<AgentModel?> getCachedAgent(String agentId);
  Future<void> cacheAgent(AgentModel agent);
  Future<void> clearCachedAgents();
}

class AgentsLocalDataSourceImpl implements AgentsLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const String cachedAgentsKey = 'CACHED_AGENTS';
  static const String cachedAgentPrefix = 'CACHED_AGENT_';

  AgentsLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<AgentModel>> getCachedAgents() async {
    final jsonString = sharedPreferences.getString(cachedAgentsKey);
    if (jsonString != null) {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => AgentModel.fromJson(json)).toList();
    }
    return [];
  }

  @override
  Future<void> cacheAgents(List<AgentModel> agents) async {
    final jsonList = agents.map((agent) => agent.toJson()).toList();
    await sharedPreferences.setString(
      cachedAgentsKey,
      json.encode(jsonList),
    );
  }

  @override
  Future<AgentModel?> getCachedAgent(String agentId) async {
    final jsonString = sharedPreferences.getString('$cachedAgentPrefix$agentId');
    if (jsonString != null) {
      return AgentModel.fromJson(json.decode(jsonString));
    }
    return null;
  }

  @override
  Future<void> cacheAgent(AgentModel agent) async {
    await sharedPreferences.setString(
      '$cachedAgentPrefix${agent.agentId}',
      json.encode(agent.toJson()),
    );
  }

  @override
  Future<void> clearCachedAgents() async {
    await sharedPreferences.remove(cachedAgentsKey);
    
    // Remove individual cached agents
    final keys = sharedPreferences.getKeys();
    for (final key in keys) {
      if (key.startsWith(cachedAgentPrefix)) {
        await sharedPreferences.remove(key);
      }
    }
  }
}
