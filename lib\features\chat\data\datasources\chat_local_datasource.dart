import 'package:hive/hive.dart';
import '../models/conversation_model.dart';
import '../models/message_model.dart';

abstract class ChatLocalDataSource {
  Future<List<ConversationModel>> getConversations({
    String? userId,
    int? limit,
    int? offset,
  });
  
  Future<ConversationModel> getConversation(String conversationId);
  Future<void> saveConversation(ConversationModel conversation);
  Future<void> updateConversation(ConversationModel conversation);
  Future<void> deleteConversation(String conversationId);
  
  Future<List<MessageModel>> getMessages({
    required String conversationId,
    int? limit,
    int? offset,
  });
  
  Future<MessageModel> getMessage(String messageId);
  Future<void> saveMessage(MessageModel message);
  Future<void> updateMessage(MessageModel message);
  Future<void> deleteMessage(String messageId);
  
  Future<List<ConversationModel>> searchConversations({
    required String query,
    String? userId,
    int? limit,
  });
  
  Future<List<MessageModel>> searchMessages({
    required String query,
    String? conversationId,
    String? userId,
    int? limit,
  });
  
  Future<String> exportConversation({
    required String conversationId,
    required String format,
  });
  
  Future<String> shareConversation(String conversationId);
  Future<void> deleteMultipleConversations(List<String> conversationIds);
  Future<void> archiveConversations(List<String> conversationIds);
  Future<Map<String, dynamic>> getConversationStats(String? userId);
  Future<List<ConversationModel>> getCachedConversations();
}

class ChatLocalDataSourceImpl implements ChatLocalDataSource {
  static const String conversationsBoxName = 'conversations';
  static const String messagesBoxName = 'messages';

  Box<ConversationModel>? _conversationsBox;
  Box<MessageModel>? _messagesBox;

  Future<Box<ConversationModel>> get conversationsBox async {
    _conversationsBox ??= await Hive.openBox<ConversationModel>(conversationsBoxName);
    return _conversationsBox!;
  }

  Future<Box<MessageModel>> get messagesBox async {
    _messagesBox ??= await Hive.openBox<MessageModel>(messagesBoxName);
    return _messagesBox!;
  }

  @override
  Future<List<ConversationModel>> getConversations({
    String? userId,
    int? limit,
    int? offset,
  }) async {
    final box = await conversationsBox;
    var conversations = box.values.toList();

    // Filter by userId if provided
    if (userId != null) {
      conversations = conversations.where((c) => c.userId == userId).toList();
    }

    // Sort by pinned first, then by last message date
    conversations.sort((a, b) {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      final aDate = a.lastMessageAt ?? a.updatedAt;
      final bDate = b.lastMessageAt ?? b.updatedAt;
      return bDate.compareTo(aDate);
    });

    // Apply pagination
    if (offset != null) {
      conversations = conversations.skip(offset).toList();
    }
    if (limit != null) {
      conversations = conversations.take(limit).toList();
    }

    return conversations;
  }

  @override
  Future<ConversationModel> getConversation(String conversationId) async {
    final box = await conversationsBox;
    final conversation = box.get(conversationId);
    if (conversation == null) {
      throw Exception('Conversation not found');
    }
    return conversation;
  }

  @override
  Future<void> saveConversation(ConversationModel conversation) async {
    final box = await conversationsBox;
    await box.put(conversation.id, conversation);
  }

  @override
  Future<void> updateConversation(ConversationModel conversation) async {
    final box = await conversationsBox;
    await box.put(conversation.id, conversation);
  }

  @override
  Future<void> deleteConversation(String conversationId) async {
    final box = await conversationsBox;
    await box.delete(conversationId);
    
    // Also delete all messages in this conversation
    final messagesBox = await this.messagesBox;
    final messagesToDelete = messagesBox.values
        .where((m) => m.conversationId == conversationId)
        .map((m) => m.id)
        .toList();
    
    for (final messageId in messagesToDelete) {
      await messagesBox.delete(messageId);
    }
  }

  @override
  Future<List<MessageModel>> getMessages({
    required String conversationId,
    int? limit,
    int? offset,
  }) async {
    final box = await messagesBox;
    var messages = box.values
        .where((m) => m.conversationId == conversationId)
        .toList();

    // Sort by timestamp
    messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Apply pagination
    if (offset != null) {
      messages = messages.skip(offset).toList();
    }
    if (limit != null) {
      messages = messages.take(limit).toList();
    }

    return messages;
  }

  @override
  Future<MessageModel> getMessage(String messageId) async {
    final box = await messagesBox;
    final message = box.get(messageId);
    if (message == null) {
      throw Exception('Message not found');
    }
    return message;
  }

  @override
  Future<void> saveMessage(MessageModel message) async {
    final box = await messagesBox;
    await box.put(message.id, message);
  }

  @override
  Future<void> updateMessage(MessageModel message) async {
    final box = await messagesBox;
    await box.put(message.id, message);
  }

  @override
  Future<void> deleteMessage(String messageId) async {
    final box = await messagesBox;
    await box.delete(messageId);
  }

  @override
  Future<List<ConversationModel>> searchConversations({
    required String query,
    String? userId,
    int? limit,
  }) async {
    final box = await conversationsBox;
    var conversations = box.values.toList();

    // Filter by userId if provided
    if (userId != null) {
      conversations = conversations.where((c) => c.userId == userId).toList();
    }

    // Search in title and description
    final queryLower = query.toLowerCase();
    conversations = conversations.where((c) {
      return c.title.toLowerCase().contains(queryLower) ||
             (c.description?.toLowerCase().contains(queryLower) ?? false);
    }).toList();

    // Sort by relevance (exact matches first, then partial matches)
    conversations.sort((a, b) {
      final aExact = a.title.toLowerCase() == queryLower ? 1 : 0;
      final bExact = b.title.toLowerCase() == queryLower ? 1 : 0;
      if (aExact != bExact) return bExact - aExact;
      
      return b.updatedAt.compareTo(a.updatedAt);
    });

    // Apply limit
    if (limit != null) {
      conversations = conversations.take(limit).toList();
    }

    return conversations;
  }

  @override
  Future<List<MessageModel>> searchMessages({
    required String query,
    String? conversationId,
    String? userId,
    int? limit,
  }) async {
    final box = await messagesBox;
    var messages = box.values.toList();

    // Filter by conversation if provided
    if (conversationId != null) {
      messages = messages.where((m) => m.conversationId == conversationId).toList();
    }

    // Search in message content
    final queryLower = query.toLowerCase();
    messages = messages.where((m) {
      return m.content.toLowerCase().contains(queryLower);
    }).toList();

    // Sort by timestamp (newest first)
    messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Apply limit
    if (limit != null) {
      messages = messages.take(limit).toList();
    }

    return messages;
  }

  @override
  Future<String> exportConversation({
    required String conversationId,
    required String format,
  }) async {
    final conversation = await getConversation(conversationId);
    final messages = await getMessages(conversationId: conversationId);

    switch (format.toLowerCase()) {
      case 'txt':
        return _exportAsText(conversation, messages);
      case 'json':
        return _exportAsJson(conversation, messages);
      case 'pdf':
        // For PDF export, you would need a PDF generation library
        return _exportAsText(conversation, messages);
      default:
        throw Exception('Unsupported export format: $format');
    }
  }

  String _exportAsText(ConversationModel conversation, List<MessageModel> messages) {
    final buffer = StringBuffer();
    buffer.writeln('Conversation: ${conversation.title}');
    buffer.writeln('Created: ${conversation.createdAt}');
    buffer.writeln('Messages: ${messages.length}');
    buffer.writeln('${'=' * 50}');
    buffer.writeln();

    for (final message in messages) {
      buffer.writeln('[${message.timestamp}] ${message.type.toUpperCase()}:');
      buffer.writeln(message.content);
      buffer.writeln();
    }

    return buffer.toString();
  }

  String _exportAsJson(ConversationModel conversation, List<MessageModel> messages) {
    final data = {
      'conversation': conversation.toJson(),
      'messages': messages.map((m) => m.toJson()).toList(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
    
    // In a real implementation, you would use dart:convert
    return data.toString();
  }

  @override
  Future<String> shareConversation(String conversationId) async {
    // In a real implementation, this would generate a shareable link
    return 'https://propimatch.com/chat/shared/$conversationId';
  }

  @override
  Future<void> deleteMultipleConversations(List<String> conversationIds) async {
    for (final id in conversationIds) {
      await deleteConversation(id);
    }
  }

  @override
  Future<void> archiveConversations(List<String> conversationIds) async {
    final box = await conversationsBox;
    for (final id in conversationIds) {
      final conversation = box.get(id);
      if (conversation != null) {
        final archived = ConversationModel(
          id: conversation.id,
          title: conversation.title,
          description: conversation.description,
          createdAt: conversation.createdAt,
          updatedAt: DateTime.now(),
          status: 'archived',
          messages: conversation.messages,
          isPinned: false,
          isFavorite: conversation.isFavorite,
          metadata: conversation.metadata,
          userId: conversation.userId,
          messageCount: conversation.messageCount,
          lastMessagePreview: conversation.lastMessagePreview,
          lastMessageAt: conversation.lastMessageAt,
          tags: conversation.tags,
        );
        await box.put(id, archived);
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getConversationStats(String? userId) async {
    final box = await conversationsBox;
    var conversations = box.values.toList();

    if (userId != null) {
      conversations = conversations.where((c) => c.userId == userId).toList();
    }

    final totalConversations = conversations.length;
    final pinnedConversations = conversations.where((c) => c.isPinned).length;
    final favoriteConversations = conversations.where((c) => c.isFavorite).length;
    final totalMessages = conversations.fold<int>(0, (sum, c) => sum + c.messageCount);

    return {
      'totalConversations': totalConversations,
      'pinnedConversations': pinnedConversations,
      'favoriteConversations': favoriteConversations,
      'totalMessages': totalMessages,
      'averageMessagesPerConversation': totalConversations > 0 ? totalMessages / totalConversations : 0,
    };
  }

  @override
  Future<List<ConversationModel>> getCachedConversations() async {
    return await getConversations();
  }
}
