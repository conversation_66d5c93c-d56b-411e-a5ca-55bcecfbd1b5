import 'package:flutter/material.dart';

/// Responsive breakpoints matching HTML design
class ResponsiveBreakpoints {
  static const double mobile = 640;
  static const double tablet = 768;
  static const double desktop = 1024;
  static const double largeDesktop = 1280;
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Responsive layout builder that adapts to screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = getDeviceType(constraints.maxWidth);
        
        switch (deviceType) {
          case DeviceType.largeDesktop:
            return largeDesktop ?? desktop ?? tablet ?? mobile;
          case DeviceType.desktop:
            return desktop ?? tablet ?? mobile;
          case DeviceType.tablet:
            return tablet ?? mobile;
          case DeviceType.mobile:
            return mobile;
        }
      },
    );
  }

  static DeviceType getDeviceType(double width) {
    if (width >= ResponsiveBreakpoints.largeDesktop) {
      return DeviceType.largeDesktop;
    } else if (width >= ResponsiveBreakpoints.desktop) {
      return DeviceType.desktop;
    } else if (width >= ResponsiveBreakpoints.tablet) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }
}

/// Extension to check device type from BuildContext
extension ResponsiveExtension on BuildContext {
  DeviceType get deviceType {
    final width = MediaQuery.of(this).size.width;
    return ResponsiveLayout.getDeviceType(width);
  }

  bool get isMobile => deviceType == DeviceType.mobile;
  bool get isTablet => deviceType == DeviceType.tablet;
  bool get isDesktop => deviceType == DeviceType.desktop || deviceType == DeviceType.largeDesktop;
  bool get isLargeDesktop => deviceType == DeviceType.largeDesktop;

  /// Get responsive padding matching HTML design
  EdgeInsets get responsivePadding {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16); // p-4
      case DeviceType.tablet:
        return const EdgeInsets.all(24); // p-6
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(40); // p-10
    }
  }

  /// Get responsive margin
  EdgeInsets get responsiveMargin {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(8);
      case DeviceType.tablet:
        return const EdgeInsets.all(12);
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(16);
    }
  }

  /// Get responsive text scale
  double get responsiveTextScale {
    switch (deviceType) {
      case DeviceType.mobile:
        return 1.0;
      case DeviceType.tablet:
        return 1.1;
      case DeviceType.desktop:
        return 1.2;
      case DeviceType.largeDesktop:
        return 1.3;
    }
  }

  /// Get responsive grid columns
  int getResponsiveColumns({
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
    int largeDesktop = 4,
  }) {
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
      case DeviceType.largeDesktop:
        return largeDesktop;
    }
  }
}

/// Responsive grid widget
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final int largeDesktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.largeDesktopColumns = 4,
    this.spacing = 16,
    this.runSpacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    final columns = context.getResponsiveColumns(
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
      largeDesktop: largeDesktopColumns,
    );

    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: children.map((child) {
        final width = (MediaQuery.of(context).size.width - 
                      (spacing * (columns - 1)) - 
                      context.responsivePadding.horizontal) / columns;
        
        return SizedBox(
          width: width,
          child: child,
        );
      }).toList(),
    );
  }
}

/// Responsive typography helper
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double mobileSize;
  final double? tabletSize;
  final double? desktopSize;
  final double? largeDesktopSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    required this.mobileSize,
    this.tabletSize,
    this.desktopSize,
    this.largeDesktopSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    
    double fontSize;
    switch (deviceType) {
      case DeviceType.mobile:
        fontSize = mobileSize;
        break;
      case DeviceType.tablet:
        fontSize = tabletSize ?? mobileSize * 1.1;
        break;
      case DeviceType.desktop:
        fontSize = desktopSize ?? mobileSize * 1.2;
        break;
      case DeviceType.largeDesktop:
        fontSize = largeDesktopSize ?? mobileSize * 1.3;
        break;
    }

    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Responsive spacing widget
class ResponsiveSpacing extends StatelessWidget {
  final double mobile;
  final double? tablet;
  final double? desktop;
  final double? largeDesktop;
  final bool isHorizontal;

  const ResponsiveSpacing({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    this.isHorizontal = false,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    
    double spacing;
    switch (deviceType) {
      case DeviceType.mobile:
        spacing = mobile;
        break;
      case DeviceType.tablet:
        spacing = tablet ?? mobile * 1.2;
        break;
      case DeviceType.desktop:
        spacing = desktop ?? mobile * 1.5;
        break;
      case DeviceType.largeDesktop:
        spacing = largeDesktop ?? mobile * 1.8;
        break;
    }

    return SizedBox(
      width: isHorizontal ? spacing : null,
      height: isHorizontal ? null : spacing,
    );
  }
}
