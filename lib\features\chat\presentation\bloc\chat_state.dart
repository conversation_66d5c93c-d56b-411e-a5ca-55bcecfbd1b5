part of 'chat_bloc.dart';

abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object?> get props => [];
}

class ChatInitial extends ChatState {}

class ChatLoading extends ChatState {}

class ChatLoaded extends ChatState {
  final List<Conversation> conversations;
  final Conversation? selectedConversation;
  final bool isSearching;
  final String searchQuery;
  final List<Conversation> searchResults;
  final bool isLoading;
  final String? error;

  const ChatLoaded({
    required this.conversations,
    this.selectedConversation,
    this.isSearching = false,
    this.searchQuery = '',
    this.searchResults = const [],
    this.isLoading = false,
    this.error,
  });

  ChatLoaded copyWith({
    List<Conversation>? conversations,
    Conversation? selectedConversation,
    bool? isSearching,
    String? searchQuery,
    List<Conversation>? searchResults,
    bool? isLoading,
    String? error,
  }) {
    return ChatLoaded(
      conversations: conversations ?? this.conversations,
      selectedConversation: selectedConversation ?? this.selectedConversation,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
      searchResults: searchResults ?? this.searchResults,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        conversations,
        selectedConversation,
        isSearching,
        searchQuery,
        searchResults,
        isLoading,
        error,
      ];
}

class ChatError extends ChatState {
  final String message;

  const ChatError({required this.message});

  @override
  List<Object> get props => [message];
}
