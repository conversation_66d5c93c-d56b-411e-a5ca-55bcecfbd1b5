import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Placeholder BLoC for dashboard
part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final dynamic getProperties; // Placeholder
  final dynamic getAgents; // Placeholder

  DashboardBloc({
    required this.getProperties,
    required this.getAgents,
  }) : super(DashboardInitial()) {
    on<DashboardLoadRequested>(_onDashboardLoadRequested);
  }

  Future<void> _onDashboardLoadRequested(
    DashboardLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(DashboardLoading());
    
    try {
      // Simulate loading dashboard data
      await Future.delayed(const Duration(milliseconds: 500));
      emit(DashboardLoaded());
    } catch (e) {
      emit(DashboardError(message: e.toString()));
    }
  }
}
