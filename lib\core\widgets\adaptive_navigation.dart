import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../responsive/responsive_layout.dart';
import '../theme/app_colors.dart';
import '../../features/auth/domain/entities/user.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';

/// Navigation item model
class NavigationItem {
  final String label;
  final IconData icon;
  final String route;
  final List<UserRole> allowedRoles;

  const NavigationItem({
    required this.label,
    required this.icon,
    required this.route,
    this.allowedRoles = const [],
  });
}

/// Adaptive navigation that switches between sidebar and bottom navigation
class AdaptiveNavigation extends StatelessWidget {
  final Widget child;
  final String currentRoute;

  const AdaptiveNavigation({
    super.key,
    required this.child,
    required this.currentRoute,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return child;
        }

        return ResponsiveLayout(
          mobile: _MobileLayout(
            child: child,
            currentRoute: currentRoute,
            user: state.user,
          ),
          desktop: _DesktopLayout(
            child: child,
            currentRoute: currentRoute,
            user: state.user,
          ),
        );
      },
    );
  }
}

/// Desktop layout with sidebar navigation
class _DesktopLayout extends StatelessWidget {
  final Widget child;
  final String currentRoute;
  final User user;

  const _DesktopLayout({
    required this.child,
    required this.currentRoute,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Sidebar Navigation
          Container(
            width: 256, // w-64 from HTML
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                right: BorderSide(
                  color: const Color(0xFFE2E8F0), // border-slate-200
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                // Header
                _buildSidebarHeader(context),
                
                // Navigation Items
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(16), // p-4
                    children: _buildNavigationItems(context, user),
                  ),
                ),
                
                // Footer
                _buildSidebarFooter(context, user),
              ],
            ),
          ),
          
          // Main Content
          Expanded(
            child: Container(
              color: AppColors.background,
              child: child,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarHeader(BuildContext context) {
    return Container(
      height: 64, // h-16
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE2E8F0), // border-slate-200
            width: 1,
          ),
        ),
      ),
      child: Center(
        child: Text(
          'PROPIMATCH',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w800,
            color: AppColors.primary,
            letterSpacing: 1.2,
          ),
        ),
      ),
    );
  }

  Widget _buildSidebarFooter(BuildContext context, User user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE2E8F0), // border-slate-200
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.primaryLight,
            backgroundImage: user.imageUrl.isNotEmpty
                ? CachedNetworkImageProvider(user.imageUrl)
                : null,
            child: user.imageUrl.isEmpty
                ? Icon(
                    Icons.person,
                    size: 20,
                    color: AppColors.primary,
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  user.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  user.userRole.name,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(0xFF64748B), // text-slate-500
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            icon: const Icon(Icons.logout),
            color: AppColors.error,
            tooltip: 'Sign Out',
          ),
        ],
      ),
    );
  }

  List<Widget> _buildNavigationItems(BuildContext context, User user) {
    final items = _getNavigationItems(user);
    
    return items.map((item) {
      final isActive = _isCurrentRoute(context, item.route);
      
      return Container(
        margin: const EdgeInsets.only(bottom: 4), // space-y-1
        child: Material(
          color: isActive ? AppColors.navItemActiveBackground : Colors.transparent,
          borderRadius: BorderRadius.circular(8), // rounded-lg
          child: InkWell(
            onTap: () => context.go(item.route),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // px-3 py-2
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    size: 20, // w-5 h-5
                    color: isActive ? AppColors.navItemActive : AppColors.navItemDefault,
                  ),
                  const SizedBox(width: 12), // ml-3
                  Text(
                    item.label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isActive ? AppColors.navItemActive : AppColors.navItemDefault,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }).toList();
  }
}

/// Mobile layout with bottom navigation
class _MobileLayout extends StatelessWidget {
  final Widget child;
  final String currentRoute;
  final User user;

  const _MobileLayout({
    required this.child,
    required this.currentRoute,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: _buildBottomNavigation(context, user),
      drawer: _buildMobileDrawer(context, user),
    );
  }

  Widget _buildBottomNavigation(BuildContext context, User user) {
    final items = _getNavigationItems(user).take(5).toList(); // Limit to 5 items
    
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE2E8F0), // border-slate-200
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 60,
          child: Row(
            children: items.map((item) {
              final isActive = _isCurrentRoute(context, item.route);
              
              return Expanded(
                child: InkWell(
                  onTap: () => context.go(item.route),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          item.icon,
                          size: 24,
                          color: isActive ? AppColors.primary : AppColors.navItemDefault,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          item.label,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                            color: isActive ? AppColors.primary : AppColors.navItemDefault,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileDrawer(BuildContext context, User user) {
    return Drawer(
      child: Column(
        children: [
          // Header
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Logo
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.home_work_outlined,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'PROPIMATCH',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w800,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                
                // User Info
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundImage: user.imageUrl.isNotEmpty
                          ? CachedNetworkImageProvider(user.imageUrl)
                          : null,
                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                      child: user.imageUrl.isEmpty
                          ? const Icon(Icons.person, color: Colors.white)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user.name,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            user.userRole.name,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: _getNavigationItems(user).map((item) {
                return ListTile(
                  leading: Icon(item.icon),
                  title: Text(item.label),
                  onTap: () {
                    Navigator.of(context).pop();
                    context.go(item.route);
                  },
                );
              }).toList(),
            ),
          ),
          
          // Logout
          ListTile(
            leading: Icon(Icons.logout, color: AppColors.error),
            title: Text(
              'Sign Out',
              style: TextStyle(color: AppColors.error),
            ),
            onTap: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
          ),
        ],
      ),
    );
  }
}

/// Get navigation items based on user role
List<NavigationItem> _getNavigationItems(User user) {
  final baseItems = [
    const NavigationItem(
      label: 'Dashboard',
      icon: Icons.dashboard,
      route: '/dashboard',
    ),
    const NavigationItem(
      label: 'Properties',
      icon: Icons.home,
      route: '/properties',
    ),
    const NavigationItem(
      label: 'AI Finder',
      icon: Icons.smart_toy,
      route: '/property-finder',
    ),
    const NavigationItem(
      label: 'Valuation',
      icon: Icons.assessment,
      route: '/valuation',
    ),
  ];

  // Add role-specific items
  switch (user.userRole) {
    case UserRole.agent:
      if (user.verificationStatus == VerificationStatus.verified) {
        baseItems.addAll([
          const NavigationItem(
            label: 'My Leads',
            icon: Icons.people,
            route: '/agent-leads',
          ),
          const NavigationItem(
            label: 'Manage Properties',
            icon: Icons.business,
            route: '/manage-properties',
          ),
        ]);
      }
      break;
    case UserRole.admin:
      baseItems.addAll([
        const NavigationItem(
          label: 'User Management',
          icon: Icons.admin_panel_settings,
          route: '/admin/users',
        ),
        const NavigationItem(
          label: 'Agent Verification',
          icon: Icons.verified_user,
          route: '/admin/agents',
        ),
      ]);
      break;
    case UserRole.investor:
      baseItems.add(
        const NavigationItem(
          label: 'Portfolio',
          icon: Icons.account_balance_wallet,
          route: '/investor-dashboard',
        ),
      );
      break;
    default:
      break;
  }

  return baseItems;
}

/// Check if current route matches navigation item
bool _isCurrentRoute(BuildContext context, String route) {
  final currentRoute = GoRouterState.of(context).uri.path;
  return currentRoute == route;
}
