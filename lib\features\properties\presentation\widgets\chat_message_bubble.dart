import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../core/theme/app_colors.dart';
import '../../../chat/domain/entities/message.dart';

class ChatMessageBubble extends StatelessWidget {
  final Message message;
  final VoidCallback? onCopy;
  final VoidCallback? onRegenerate;
  final VoidCallback? onDelete;

  const ChatMessageBubble({
    super.key,
    required this.message,
    this.onCopy,
    this.onRegenerate,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.type == MessageType.user;
    final isAssistant = message.type == MessageType.assistant;
    final isSystem = message.type == MessageType.system;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            _buildAvatar(context, isAssistant, isSystem),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // Message Header
                Row(
                  mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
                  children: [
                    if (!isUser) ...[
                      Text(
                        isAssistant ? 'AI Assistant' : 'System',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                    ],
                    if (isUser) const Spacer(),
                    Text(
                      timeago.format(message.timestamp),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                        fontSize: 11,
                      ),
                    ),
                    if (message.isEdited) ...[
                      const SizedBox(width: 4),
                      Icon(
                        Icons.edit,
                        size: 12,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ],
                    if (message.status == MessageStatus.sending) ...[
                      const SizedBox(width: 4),
                      SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                    if (message.status == MessageStatus.failed) ...[
                      const SizedBox(width: 4),
                      Icon(
                        Icons.error_outline,
                        size: 12,
                        color: AppColors.error,
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                
                // Message Bubble
                Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.8,
                  ),
                  decoration: BoxDecoration(
                    color: _getBubbleColor(isUser, isAssistant, isSystem),
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(16),
                      topRight: const Radius.circular(16),
                      bottomLeft: Radius.circular(isUser ? 16 : 4),
                      bottomRight: Radius.circular(isUser ? 4 : 16),
                    ),
                    border: Border.all(
                      color: _getBorderColor(isUser, isAssistant, isSystem),
                      width: 1,
                    ),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Message Content
                      SelectableText(
                        message.content,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _getTextColor(isUser, isAssistant, isSystem),
                          height: 1.4,
                        ),
                      ),
                      
                      // Attachments
                      if (message.attachments != null && message.attachments!.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: message.attachments!.map((attachment) {
                            return Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.attach_file,
                                    size: 16,
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    attachment,
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Message Actions
                if (!isSystem) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
                    children: [
                      if (!isUser) ...[
                        _buildActionButton(
                          context,
                          Icons.content_copy,
                          'Copy',
                          onCopy,
                        ),
                        if (onRegenerate != null) ...[
                          const SizedBox(width: 8),
                          _buildActionButton(
                            context,
                            Icons.refresh,
                            'Regenerate',
                            onRegenerate,
                          ),
                        ],
                      ],
                      if (isUser) ...[
                        _buildActionButton(
                          context,
                          Icons.content_copy,
                          'Copy',
                          onCopy,
                        ),
                      ],
                      if (onDelete != null) ...[
                        const SizedBox(width: 8),
                        _buildActionButton(
                          context,
                          Icons.delete_outline,
                          'Delete',
                          onDelete,
                          color: AppColors.error,
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 12),
            _buildAvatar(context, false, false, isUser: true),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, bool isAssistant, bool isSystem, {bool isUser = false}) {
    Color backgroundColor;
    IconData icon;
    Color iconColor;

    if (isUser) {
      backgroundColor = AppColors.primary;
      icon = Icons.person;
      iconColor = Colors.white;
    } else if (isAssistant) {
      backgroundColor = AppColors.secondary.withValues(alpha: 0.1);
      icon = Icons.smart_toy;
      iconColor = AppColors.secondary;
    } else {
      backgroundColor = AppColors.warning.withValues(alpha: 0.1);
      icon = Icons.info_outline;
      iconColor = AppColors.warning;
    }

    return CircleAvatar(
      radius: 16,
      backgroundColor: backgroundColor,
      child: Icon(
        icon,
        size: 16,
        color: iconColor,
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    IconData icon,
    String tooltip,
    VoidCallback? onPressed, {
    Color? color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(4),
        child: Icon(
          icon,
          size: 16,
          color: color ?? AppColors.onSurfaceVariant,
        ),
      ),
    );
  }

  Color _getBubbleColor(bool isUser, bool isAssistant, bool isSystem) {
    if (isUser) {
      return AppColors.primary;
    } else if (isAssistant) {
      return AppColors.surface;
    } else {
      return AppColors.warning.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor(bool isUser, bool isAssistant, bool isSystem) {
    if (isUser) {
      return AppColors.primary;
    } else if (isAssistant) {
      return AppColors.outline.withValues(alpha: 0.3);
    } else {
      return AppColors.warning.withValues(alpha: 0.3);
    }
  }

  Color _getTextColor(bool isUser, bool isAssistant, bool isSystem) {
    if (isUser) {
      return Colors.white;
    } else {
      return AppColors.onSurface;
    }
  }
}
