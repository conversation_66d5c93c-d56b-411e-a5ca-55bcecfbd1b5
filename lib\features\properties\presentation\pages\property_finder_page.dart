import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../chat/presentation/bloc/chat_bloc.dart';
import '../../../chat/domain/entities/conversation.dart';
import '../../../chat/domain/entities/message.dart';
import '../widgets/chat_conversation_list.dart';
import '../widgets/chat_message_bubble.dart';
import '../widgets/chat_input_field.dart';

class PropertyFinderPage extends StatefulWidget {
  const PropertyFinderPage({super.key});

  @override
  State<PropertyFinderPage> createState() => _PropertyFinderPageState();
}

class _PropertyFinderPageState extends State<PropertyFinderPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _messagesScrollController = ScrollController();
  bool _showConversationList = false;

  @override
  void initState() {
    super.initState();
    // Load conversations when page initializes
    context.read<ChatBloc>().add(const ChatLoadConversations());
  }

  @override
  void dispose() {
    _messageController.dispose();
    _messagesScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ChatBloc, ChatState>(
      listener: (context, state) {
        if (state is ChatLoaded && state.selectedConversation != null) {
          // Scroll to bottom when new messages arrive
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_messagesScrollController.hasClients) {
              _messagesScrollController.animateTo(
                _messagesScrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        }
      },
      child: ResponsiveLayout(
        mobile: _buildMobileLayout(context),
        tablet: _buildTabletLayout(context),
        desktop: _buildDesktopLayout(context),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildMobileAppBar(context),
      drawer: _buildConversationDrawer(context),
      body: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          if (state is ChatLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ChatError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(state.message, style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.read<ChatBloc>().add(const ChatLoadConversations()),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is ChatLoaded) {
            return Column(
              children: [
                // Chat Messages Area
                Expanded(
                  child: state.selectedConversation != null
                      ? _buildChatMessages(context, state.selectedConversation!)
                      : _buildWelcomeScreen(context),
                ),

                // Input Area
                _buildChatInput(context, state),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "filters",
            onPressed: () => _showFiltersBottomSheet(context),
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
            child: const Icon(Icons.tune),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "new_chat",
            onPressed: () => _createNewConversation(context),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Filters Toggle Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(
                  color: AppColors.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'AI Property Finder',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showFiltersBottomSheet(context),
                  icon: const Icon(Icons.tune),
                  label: const Text('Filters'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Chat Interface
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 20),
                  _buildSuggestionChips(context),
                  const Spacer(),
                ],
              ),
            ),
          ),
          _buildInputArea(context),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          // Chat Area
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 24),
                  _buildSuggestionChips(context),
                  const Spacer(),
                  _buildInputArea(context),
                ],
              ),
            ),
          ),

          // Sidebar with filters/results (Desktop only)
          Container(
            width: 320,
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                left: BorderSide(
                  color: AppColors.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: _buildFiltersSidebar(context),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage(BuildContext context) {
    return EnhancedCard(
      backgroundColor: AppColors.primaryLight,
      borderRadius: BorderRadius.circular(12),
      padding: EdgeInsets.all(context.isDesktop ? 24 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.smart_toy,
                color: AppColors.primary,
                size: context.isDesktop ? 28 : 24,
              ),
              const SizedBox(width: 8),
              ResponsiveText(
                'AI Assistant',
                mobileSize: 16,
                desktopSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ],
          ),
          ResponsiveSpacing(mobile: 8, desktop: 12),
          ResponsiveText(
            'Hello! I\'m your AI property assistant. Tell me what you\'re looking for and I\'ll help you find the perfect property.',
            mobileSize: 14,
            desktopSize: 16,
            color: AppColors.onBackground,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionChips(BuildContext context) {
    final suggestions = [
      'Find 3BR apartments under \$500K',
      'Show luxury villas in Marina',
      'Investment properties with high ROI',
      'Properties near metro stations',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) =>
        _buildSuggestionChip(context, suggestion)
      ).toList(),
    );
  }

  Widget _buildFiltersSidebar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Filters',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 24),

          // Price Range
          _buildFilterSection(
            context,
            'Price Range',
            Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Min Price',
                    prefixText: '\$',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Max Price',
                    prefixText: '\$',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Property Type
          _buildFilterSection(
            context,
            'Property Type',
            Column(
              children: [
                CheckboxListTile(
                  title: const Text('Apartment'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Villa'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Townhouse'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Penthouse'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Bedrooms
          _buildFilterSection(
            context,
            'Bedrooms',
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: List.generate(6, (index) {
                final bedrooms = index == 5 ? '5+' : '${index + 1}';
                return FilterChip(
                  label: Text(bedrooms),
                  selected: false,
                  onSelected: (selected) {},
                );
              }),
            ),
          ),

          const SizedBox(height: 24),

          // Location
          _buildFilterSection(
            context,
            'Location',
            Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Enter location',
                    prefixIcon: Icon(Icons.location_on),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                CheckboxListTile(
                  title: const Text('Near Metro'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Near Schools'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildInputArea(BuildContext context) {
    if (context.isDesktop) {
      // Desktop input area (inline)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.outline.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Ask me about properties...',
                  border: InputBorder.none,
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () {
                  // Send message
                },
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Mobile input area (bottom)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(
              color: AppColors.outline.withValues(alpha: 0.3),
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Ask me about properties...',
                    hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF9CA3AF),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    filled: true,
                    fillColor: AppColors.surface,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () {
                    // Send message
                  },
                  icon: const Icon(
                    Icons.send,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
          


  Widget _buildSuggestionChip(BuildContext context, String text) {
    return Material(
      color: AppColors.surfaceVariant,
      borderRadius: BorderRadius.circular(20),
      child: InkWell(
        onTap: () {
          // Handle suggestion tap
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.isDesktop ? 16 : 12,
            vertical: context.isDesktop ? 10 : 8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.outline, width: 0.5),
          ),
          child: ResponsiveText(
            text,
            mobileSize: 12,
            desktopSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),
      ),
    );
  }

  /// Show filters in a bottom sheet for mobile and tablet
  void _showFiltersBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Search Filters',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Filters Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Price Range
                    _buildFilterSection(
                      context,
                      'Price Range',
                      Column(
                        children: [
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Min Price',
                              prefixText: '\$',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Max Price',
                              prefixText: '\$',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Property Type
                    _buildFilterSection(
                      context,
                      'Property Type',
                      Column(
                        children: [
                          CheckboxListTile(
                            title: const Text('Apartment'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Villa'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Townhouse'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Penthouse'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Bedrooms
                    _buildFilterSection(
                      context,
                      'Bedrooms',
                      Wrap(
                        spacing: 8,
                        children: List.generate(6, (index) {
                          final bedrooms = index == 5 ? '5+' : '${index + 1}';
                          return FilterChip(
                            label: Text(bedrooms),
                            selected: false,
                            onSelected: (selected) {},
                          );
                        }),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Location
                    _buildFilterSection(
                      context,
                      'Location',
                      Column(
                        children: [
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Enter location or area',
                              prefixIcon: Icon(Icons.location_on),
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          CheckboxListTile(
                            title: const Text('Near Metro Station'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Near Schools'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Near Shopping Centers'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // Apply Button
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // Clear filters
                        Navigator.of(context).pop();
                      },
                      child: const Text('Clear All'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply filters
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Apply Filters'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Mobile App Bar
  PreferredSizeWidget _buildMobileAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.surface,
      elevation: 1,
      title: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          if (state is ChatLoaded && state.selectedConversation != null) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.selectedConversation!.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${state.selectedConversation!.messageCount} messages',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            );
          }
          return const Text('AI Property Finder');
        },
      ),
      actions: [
        BlocBuilder<ChatBloc, ChatState>(
          builder: (context, state) {
            if (state is ChatLoaded && state.selectedConversation != null) {
              return PopupMenuButton<String>(
                onSelected: (value) => _handleConversationAction(context, value, state.selectedConversation!),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'rename', child: Text('Rename')),
                  PopupMenuItem(
                    value: 'pin',
                    child: Text(state.selectedConversation!.isPinned ? 'Unpin' : 'Pin'),
                  ),
                  PopupMenuItem(
                    value: 'favorite',
                    child: Text(state.selectedConversation!.isFavorite ? 'Unfavorite' : 'Favorite'),
                  ),
                  const PopupMenuItem(value: 'export', child: Text('Export')),
                  const PopupMenuItem(value: 'share', child: Text('Share')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                ],
              );
            }
            return IconButton(
              onPressed: () => _showSearchDialog(context),
              icon: const Icon(Icons.search),
            );
          },
        ),
      ],
    );
  }

  // Conversation Drawer
  Widget _buildConversationDrawer(BuildContext context) {
    return Drawer(
      child: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          if (state is ChatLoaded) {
            return ChatConversationList(
              conversations: state.isSearching ? state.searchResults : state.conversations,
              selectedConversation: state.selectedConversation,
              isSearching: state.isSearching,
              searchQuery: state.searchQuery,
              onConversationSelected: (conversation) {
                Navigator.of(context).pop();
                context.read<ChatBloc>().add(ChatSelectConversation(conversation: conversation));
              },
              onConversationAction: (action, conversation) {
                Navigator.of(context).pop();
                _handleConversationAction(context, action, conversation);
              },
              onSearch: (query) {
                context.read<ChatBloc>().add(ChatSearchConversations(query: query));
              },
              onClearSearch: () {
                context.read<ChatBloc>().add(const ChatClearSearch());
              },
            );
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  // Chat Messages
  Widget _buildChatMessages(BuildContext context, Conversation conversation) {
    if (conversation.messages.isEmpty) {
      return _buildWelcomeScreen(context);
    }

    return ListView.builder(
      controller: _messagesScrollController,
      padding: const EdgeInsets.all(16),
      itemCount: conversation.messages.length,
      itemBuilder: (context, index) {
        final message = conversation.messages[index];
        return ChatMessageBubble(
          message: message,
          onCopy: () => _copyMessage(message.content),
          onRegenerate: message.type == MessageType.assistant
              ? () => _regenerateMessage(context, message.id)
              : null,
          onDelete: () => _deleteMessage(context, message.id),
        );
      },
    );
  }

  // Welcome Screen
  Widget _buildWelcomeScreen(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const SizedBox(height: 40),
          _buildWelcomeMessage(context),
          const SizedBox(height: 24),
          _buildSuggestionChips(context),
          const SizedBox(height: 40),
          _buildQuickActions(context),
        ],
      ),
    );
  }

  // Quick Actions
  Widget _buildQuickActions(BuildContext context) {
    final actions = [
      {'title': 'Find Apartments', 'icon': Icons.apartment, 'query': 'Show me available apartments'},
      {'title': 'Luxury Villas', 'icon': Icons.villa, 'query': 'Find luxury villas in prime locations'},
      {'title': 'Investment Properties', 'icon': Icons.trending_up, 'query': 'Show investment properties with high ROI'},
      {'title': 'Near Metro', 'icon': Icons.train, 'query': 'Properties near metro stations'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: actions.length,
          itemBuilder: (context, index) {
            final action = actions[index];
            return EnhancedCard(
              onTap: () => _sendQuickMessage(context, action['query'] as String),
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    action['icon'] as IconData,
                    size: 32,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    action['title'] as String,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // Chat Input
  Widget _buildChatInput(BuildContext context, ChatLoaded state) {
    return ChatInputField(
      controller: _messageController,
      onSend: (message) => _sendMessage(context, message),
      isLoading: state.isLoading,
      enabled: state.selectedConversation != null,
      hintText: state.selectedConversation != null
          ? 'Ask about properties...'
          : 'Create a new conversation to start chatting',
    );
  }

  // Action Handlers
  void _handleConversationAction(BuildContext context, String action, Conversation conversation) {
    switch (action) {
      case 'rename':
        _showRenameDialog(context, conversation);
        break;
      case 'pin':
        context.read<ChatBloc>().add(ChatPinConversation(
          conversationId: conversation.id,
          isPinned: !conversation.isPinned,
        ));
        break;
      case 'favorite':
        context.read<ChatBloc>().add(ChatFavoriteConversation(
          conversationId: conversation.id,
          isFavorite: !conversation.isFavorite,
        ));
        break;
      case 'export':
        _exportConversation(context, conversation);
        break;
      case 'share':
        _shareConversation(context, conversation);
        break;
      case 'delete':
        _showDeleteDialog(context, conversation);
        break;
    }
  }

  void _createNewConversation(BuildContext context) {
    context.read<ChatBloc>().add(const ChatCreateConversation(
      title: 'New Property Search',
    ));
  }

  void _sendMessage(BuildContext context, String message) {
    if (message.trim().isEmpty) return;

    final state = context.read<ChatBloc>().state;
    if (state is ChatLoaded) {
      if (state.selectedConversation == null) {
        // Create new conversation with first message
        context.read<ChatBloc>().add(ChatCreateConversation(
          title: message.length > 30 ? '${message.substring(0, 30)}...' : message,
          firstMessage: message,
        ));
      }

      context.read<ChatBloc>().add(ChatSendMessage(content: message));
      _messageController.clear();
    }
  }

  void _sendQuickMessage(BuildContext context, String message) {
    _messageController.text = message;
    _sendMessage(context, message);
  }

  void _copyMessage(String content) {
    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Message copied to clipboard')),
    );
  }

  void _regenerateMessage(BuildContext context, String messageId) {
    context.read<ChatBloc>().add(ChatRegenerateMessage(messageId: messageId));
  }

  void _deleteMessage(BuildContext context, String messageId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Message'),
        content: const Text('Are you sure you want to delete this message?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ChatBloc>().add(ChatDeleteMessage(messageId: messageId));
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showRenameDialog(BuildContext context, Conversation conversation) {
    final controller = TextEditingController(text: conversation.title);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Conversation'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Conversation Title',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                Navigator.of(context).pop();
                context.read<ChatBloc>().add(ChatRenameConversation(
                  conversationId: conversation.id,
                  newTitle: controller.text.trim(),
                ));
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Conversation conversation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Conversation'),
        content: Text('Are you sure you want to delete "${conversation.title}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ChatBloc>().add(ChatDeleteConversation(conversationId: conversation.id));
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Conversations'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'Search...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onChanged: (query) {
            context.read<ChatBloc>().add(ChatSearchConversations(query: query));
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ChatBloc>().add(const ChatClearSearch());
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportConversation(BuildContext context, Conversation conversation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Conversation'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ChatBloc>().add(ChatExportConversation(
                conversationId: conversation.id,
                format: 'txt',
              ));
            },
            child: const Text('Text'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ChatBloc>().add(ChatExportConversation(
                conversationId: conversation.id,
                format: 'json',
              ));
            },
            child: const Text('JSON'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _shareConversation(BuildContext context, Conversation conversation) {
    context.read<ChatBloc>().add(ChatShareConversation(conversationId: conversation.id));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share link copied to clipboard')),
    );
  }
}
