import 'package:flutter/material.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/theme/app_colors.dart';

class PropertyFinderPage extends StatelessWidget {
  const PropertyFinderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(context),
      desktop: _buildDesktopLayout(context),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Chat Interface - Full screen like HTML
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 16),
                  _buildSuggestionChips(context),
                  const Spacer(),
                ],
              ),
            ),
          ),
          _buildInputArea(context),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          // Chat Area
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 24),
                  _buildSuggestionChips(context),
                  const Spacer(),
                  _buildInputArea(context),
                ],
              ),
            ),
          ),

          // Sidebar with filters/results (Desktop only)
          Container(
            width: 320,
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                left: BorderSide(
                  color: AppColors.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: _buildFiltersSidebar(context),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage(BuildContext context) {
    return EnhancedCard(
      backgroundColor: AppColors.primaryLight,
      borderRadius: BorderRadius.circular(12),
      padding: EdgeInsets.all(context.isDesktop ? 24 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.smart_toy,
                color: AppColors.primary,
                size: context.isDesktop ? 28 : 24,
              ),
              const SizedBox(width: 8),
              ResponsiveText(
                'AI Assistant',
                mobileSize: 16,
                desktopSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ],
          ),
          ResponsiveSpacing(mobile: 8, desktop: 12),
          ResponsiveText(
            'Hello! I\'m your AI property assistant. Tell me what you\'re looking for and I\'ll help you find the perfect property.',
            mobileSize: 14,
            desktopSize: 16,
            color: AppColors.onBackground,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionChips(BuildContext context) {
    final suggestions = [
      'Find 3BR apartments under \$500K',
      'Show luxury villas in Marina',
      'Investment properties with high ROI',
      'Properties near metro stations',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) =>
        _buildSuggestionChip(context, suggestion)
      ).toList(),
    );
  }

  Widget _buildFiltersSidebar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Filters',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 24),

          // Price Range
          _buildFilterSection(
            context,
            'Price Range',
            Column(
              children: [
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Min Price',
                    prefixText: '\$',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Max Price',
                    prefixText: '\$',
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Property Type
          _buildFilterSection(
            context,
            'Property Type',
            Column(
              children: [
                CheckboxListTile(
                  title: const Text('Apartment'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Villa'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Townhouse'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildInputArea(BuildContext context) {
    if (context.isDesktop) {
      // Desktop input area (inline)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.outline.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Ask me about properties...',
                  border: InputBorder.none,
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () {
                  // Send message
                },
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Mobile input area (bottom)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(
              color: AppColors.outline.withValues(alpha: 0.3),
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Ask me about properties...',
                    hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF9CA3AF),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    filled: true,
                    fillColor: AppColors.surface,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () {
                    // Send message
                  },
                  icon: const Icon(
                    Icons.send,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
          


  Widget _buildSuggestionChip(BuildContext context, String text) {
    return Material(
      color: AppColors.surfaceVariant,
      borderRadius: BorderRadius.circular(20),
      child: InkWell(
        onTap: () {
          // Handle suggestion tap
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.isDesktop ? 16 : 12,
            vertical: context.isDesktop ? 10 : 8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.outline, width: 0.5),
          ),
          child: ResponsiveText(
            text,
            mobileSize: 12,
            desktopSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),
      ),
    );
  }
}
