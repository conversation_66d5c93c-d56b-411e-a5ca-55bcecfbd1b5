import 'package:flutter/material.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/theme/app_colors.dart';

class PropertyFinderPage extends StatelessWidget {
  const PropertyFinderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(context),
      tablet: _buildTabletLayout(context),
      desktop: _buildDesktopLayout(context),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Chat Interface - Full screen like HTML
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 16),
                  _buildSuggestionChips(context),
                  const Spacer(),
                ],
              ),
            ),
          ),
          _buildInputArea(context),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showFiltersBottomSheet(context),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.tune),
        label: const Text('Filters'),
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Filters Toggle Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(
                  color: AppColors.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'AI Property Finder',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showFiltersBottomSheet(context),
                  icon: const Icon(Icons.tune),
                  label: const Text('Filters'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Chat Interface
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 20),
                  _buildSuggestionChips(context),
                  const Spacer(),
                ],
              ),
            ),
          ),
          _buildInputArea(context),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          // Chat Area
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  _buildWelcomeMessage(context),
                  const SizedBox(height: 24),
                  _buildSuggestionChips(context),
                  const Spacer(),
                  _buildInputArea(context),
                ],
              ),
            ),
          ),

          // Sidebar with filters/results (Desktop only)
          Container(
            width: 320,
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                left: BorderSide(
                  color: AppColors.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: _buildFiltersSidebar(context),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage(BuildContext context) {
    return EnhancedCard(
      backgroundColor: AppColors.primaryLight,
      borderRadius: BorderRadius.circular(12),
      padding: EdgeInsets.all(context.isDesktop ? 24 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.smart_toy,
                color: AppColors.primary,
                size: context.isDesktop ? 28 : 24,
              ),
              const SizedBox(width: 8),
              ResponsiveText(
                'AI Assistant',
                mobileSize: 16,
                desktopSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ],
          ),
          ResponsiveSpacing(mobile: 8, desktop: 12),
          ResponsiveText(
            'Hello! I\'m your AI property assistant. Tell me what you\'re looking for and I\'ll help you find the perfect property.',
            mobileSize: 14,
            desktopSize: 16,
            color: AppColors.onBackground,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionChips(BuildContext context) {
    final suggestions = [
      'Find 3BR apartments under \$500K',
      'Show luxury villas in Marina',
      'Investment properties with high ROI',
      'Properties near metro stations',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) =>
        _buildSuggestionChip(context, suggestion)
      ).toList(),
    );
  }

  Widget _buildFiltersSidebar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Filters',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 24),

          // Price Range
          _buildFilterSection(
            context,
            'Price Range',
            Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Min Price',
                    prefixText: '\$',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Max Price',
                    prefixText: '\$',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Property Type
          _buildFilterSection(
            context,
            'Property Type',
            Column(
              children: [
                CheckboxListTile(
                  title: const Text('Apartment'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Villa'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Townhouse'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Penthouse'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Bedrooms
          _buildFilterSection(
            context,
            'Bedrooms',
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: List.generate(6, (index) {
                final bedrooms = index == 5 ? '5+' : '${index + 1}';
                return FilterChip(
                  label: Text(bedrooms),
                  selected: false,
                  onSelected: (selected) {},
                );
              }),
            ),
          ),

          const SizedBox(height: 24),

          // Location
          _buildFilterSection(
            context,
            'Location',
            Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'Enter location',
                    prefixIcon: Icon(Icons.location_on),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                CheckboxListTile(
                  title: const Text('Near Metro'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
                CheckboxListTile(
                  title: const Text('Near Schools'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildInputArea(BuildContext context) {
    if (context.isDesktop) {
      // Desktop input area (inline)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.outline.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Ask me about properties...',
                  border: InputBorder.none,
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () {
                  // Send message
                },
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Mobile input area (bottom)
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(
              color: AppColors.outline.withValues(alpha: 0.3),
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Ask me about properties...',
                    hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF9CA3AF),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.inputBorder),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    filled: true,
                    fillColor: AppColors.surface,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () {
                    // Send message
                  },
                  icon: const Icon(
                    Icons.send,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
          


  Widget _buildSuggestionChip(BuildContext context, String text) {
    return Material(
      color: AppColors.surfaceVariant,
      borderRadius: BorderRadius.circular(20),
      child: InkWell(
        onTap: () {
          // Handle suggestion tap
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.isDesktop ? 16 : 12,
            vertical: context.isDesktop ? 10 : 8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.outline, width: 0.5),
          ),
          child: ResponsiveText(
            text,
            mobileSize: 12,
            desktopSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),
      ),
    );
  }

  /// Show filters in a bottom sheet for mobile and tablet
  void _showFiltersBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Search Filters',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Filters Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Price Range
                    _buildFilterSection(
                      context,
                      'Price Range',
                      Column(
                        children: [
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Min Price',
                              prefixText: '\$',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Max Price',
                              prefixText: '\$',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Property Type
                    _buildFilterSection(
                      context,
                      'Property Type',
                      Column(
                        children: [
                          CheckboxListTile(
                            title: const Text('Apartment'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Villa'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Townhouse'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Penthouse'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Bedrooms
                    _buildFilterSection(
                      context,
                      'Bedrooms',
                      Wrap(
                        spacing: 8,
                        children: List.generate(6, (index) {
                          final bedrooms = index == 5 ? '5+' : '${index + 1}';
                          return FilterChip(
                            label: Text(bedrooms),
                            selected: false,
                            onSelected: (selected) {},
                          );
                        }),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Location
                    _buildFilterSection(
                      context,
                      'Location',
                      Column(
                        children: [
                          TextField(
                            decoration: const InputDecoration(
                              hintText: 'Enter location or area',
                              prefixIcon: Icon(Icons.location_on),
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          CheckboxListTile(
                            title: const Text('Near Metro Station'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Near Schools'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                          CheckboxListTile(
                            title: const Text('Near Shopping Centers'),
                            value: false,
                            onChanged: (value) {},
                            dense: true,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // Apply Button
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // Clear filters
                        Navigator.of(context).pop();
                      },
                      child: const Text('Clear All'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply filters
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Apply Filters'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
