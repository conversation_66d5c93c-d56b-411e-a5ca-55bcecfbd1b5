import 'package:hive/hive.dart';
import '../../domain/entities/message.dart';

part 'message_model.g.dart';

@HiveType(typeId: 1)
class MessageModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String content;

  @HiveField(2)
  final String type;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final String status;

  @HiveField(5)
  final String? conversationId;

  @HiveField(6)
  final Map<String, dynamic>? metadata;

  @HiveField(7)
  final bool isEdited;

  @HiveField(8)
  final DateTime? editedAt;

  @HiveField(9)
  final List<String>? attachments;

  @HiveField(10)
  final String? parentMessageId;

  MessageModel({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    required this.status,
    this.conversationId,
    this.metadata,
    this.isEdited = false,
    this.editedAt,
    this.attachments,
    this.parentMessageId,
  });

  factory MessageModel.fromEntity(Message message) {
    return MessageModel(
      id: message.id,
      content: message.content,
      type: message.type.name,
      timestamp: message.timestamp,
      status: message.status.name,
      conversationId: message.conversationId,
      metadata: message.metadata,
      isEdited: message.isEdited,
      editedAt: message.editedAt,
      attachments: message.attachments,
      parentMessageId: message.parentMessageId,
    );
  }

  Message toEntity() {
    return Message(
      id: id,
      content: content,
      type: MessageType.values.firstWhere(
        (e) => e.name == type,
        orElse: () => MessageType.user,
      ),
      timestamp: timestamp,
      status: MessageStatus.values.firstWhere(
        (e) => e.name == status,
        orElse: () => MessageStatus.sent,
      ),
      conversationId: conversationId,
      metadata: metadata,
      isEdited: isEdited,
      editedAt: editedAt,
      attachments: attachments,
      parentMessageId: parentMessageId,
    );
  }

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as String,
      content: json['content'] as String,
      type: json['type'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: json['status'] as String,
      conversationId: json['conversationId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isEdited: json['isEdited'] as bool? ?? false,
      editedAt: json['editedAt'] != null 
          ? DateTime.parse(json['editedAt'] as String)
          : null,
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>(),
      parentMessageId: json['parentMessageId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'status': status,
      'conversationId': conversationId,
      'metadata': metadata,
      'isEdited': isEdited,
      'editedAt': editedAt?.toIso8601String(),
      'attachments': attachments,
      'parentMessageId': parentMessageId,
    };
  }
}
