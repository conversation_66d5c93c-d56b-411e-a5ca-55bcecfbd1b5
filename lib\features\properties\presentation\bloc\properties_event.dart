part of 'properties_bloc.dart';

abstract class PropertiesEvent extends Equatable {
  const PropertiesEvent();

  @override
  List<Object?> get props => [];
}

class PropertiesLoadRequested extends PropertiesEvent {
  final PropertyType? type;
  final PropertyStatus? status;
  final double? minPrice;
  final double? maxPrice;
  final int? minBedrooms;
  final int? maxBedrooms;

  const PropertiesLoadRequested({
    this.type,
    this.status,
    this.minPrice,
    this.maxPrice,
    this.minBedrooms,
    this.maxBedrooms,
  });

  @override
  List<Object?> get props => [
        type,
        status,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
      ];
}

class PropertyDetailsLoadRequested extends PropertiesEvent {
  final String propertyId;

  const PropertyDetailsLoadRequested({required this.propertyId});

  @override
  List<Object> get props => [propertyId];
}

class PropertiesSearchRequested extends PropertiesEvent {
  final String query;

  const PropertiesSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class PropertyAddRequested extends PropertiesEvent {
  final Property property;

  const PropertyAddRequested({required this.property});

  @override
  List<Object> get props => [property];
}

class PropertyUpdateRequested extends PropertiesEvent {
  final Property property;

  const PropertyUpdateRequested({required this.property});

  @override
  List<Object> get props => [property];
}

class PropertyDeleteRequested extends PropertiesEvent {
  final String propertyId;

  const PropertyDeleteRequested({required this.propertyId});

  @override
  List<Object> get props => [propertyId];
}

class PropertiesFilterChanged extends PropertiesEvent {
  final PropertyType? type;
  final PropertyStatus? status;
  final double? minPrice;
  final double? maxPrice;
  final int? minBedrooms;
  final int? maxBedrooms;

  const PropertiesFilterChanged({
    this.type,
    this.status,
    this.minPrice,
    this.maxPrice,
    this.minBedrooms,
    this.maxBedrooms,
  });

  @override
  List<Object?> get props => [
        type,
        status,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
      ];
}
