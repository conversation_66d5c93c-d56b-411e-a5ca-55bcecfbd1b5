import '../../domain/entities/user.dart';

// part 'user_model.g.dart'; // Uncomment after running build_runner

// @JsonSerializable() // Uncomment after running build_runner
class UserModel extends User {
  const UserModel({
    required super.uid,
    required super.name,
    required super.email,
    required super.phone,
    required super.location,
    required super.userRole,
    required super.status,
    required super.imageUrl,
    super.verificationStatus,
    super.credits,
    super.recentlyViewed,
    super.specialization,
    super.languages,
    super.rating,
    super.experience,
    super.propertiesSold,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      location: json['location'] as String,
      userRole: UserRole.values.firstWhere((e) => e.toString() == 'UserRole.${json['userRole']}'),
      status: UserStatus.values.firstWhere((e) => e.toString() == 'UserStatus.${json['status']}'),
      imageUrl: json['imageUrl'] as String,
      verificationStatus: json['verificationStatus'] != null
          ? VerificationStatus.values.firstWhere((e) => e.toString() == 'VerificationStatus.${json['verificationStatus']}')
          : null,
      credits: json['credits'] as int?,
      recentlyViewed: (json['recentlyViewed'] as List<dynamic>?)?.cast<String>(),
      specialization: (json['specialization'] as List<dynamic>?)?.cast<String>(),
      languages: (json['languages'] as List<dynamic>?)?.cast<String>(),
      rating: (json['rating'] as num?)?.toDouble(),
      experience: json['experience'] as int?,
      propertiesSold: json['propertiesSold'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'phone': phone,
      'location': location,
      'userRole': userRole.toString().split('.').last,
      'status': status.toString().split('.').last,
      'imageUrl': imageUrl,
      'verificationStatus': verificationStatus?.toString().split('.').last,
      'credits': credits,
      'recentlyViewed': recentlyViewed,
      'specialization': specialization,
      'languages': languages,
      'rating': rating,
      'experience': experience,
      'propertiesSold': propertiesSold,
    };
  }

  factory UserModel.fromEntity(User user) {
    return UserModel(
      uid: user.uid,
      name: user.name,
      email: user.email,
      phone: user.phone,
      location: user.location,
      userRole: user.userRole,
      status: user.status,
      imageUrl: user.imageUrl,
      verificationStatus: user.verificationStatus,
      credits: user.credits,
      recentlyViewed: user.recentlyViewed,
      specialization: user.specialization,
      languages: user.languages,
      rating: user.rating,
      experience: user.experience,
      propertiesSold: user.propertiesSold,
    );
  }

  User toEntity() {
    return User(
      uid: uid,
      name: name,
      email: email,
      phone: phone,
      location: location,
      userRole: userRole,
      status: status,
      imageUrl: imageUrl,
      verificationStatus: verificationStatus,
      credits: credits,
      recentlyViewed: recentlyViewed,
      specialization: specialization,
      languages: languages,
      rating: rating,
      experience: experience,
      propertiesSold: propertiesSold,
    );
  }
}
