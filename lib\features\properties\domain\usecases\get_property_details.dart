import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/property.dart';
import '../repositories/properties_repository.dart';

class GetPropertyDetails implements UseCase<Property, GetPropertyDetailsParams> {
  final PropertiesRepository repository;

  GetPropertyDetails(this.repository);

  @override
  Future<Either<Failure, Property>> call(GetPropertyDetailsParams params) async {
    return await repository.getPropertyDetails(params.propertyId);
  }
}

class GetPropertyDetailsParams extends Equatable {
  final String propertyId;

  const GetPropertyDetailsParams({required this.propertyId});

  @override
  List<Object> get props => [propertyId];
}
