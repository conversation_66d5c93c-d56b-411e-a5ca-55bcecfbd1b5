import 'package:equatable/equatable.dart';
import '../../../auth/domain/entities/user.dart';

class Agent extends Equatable {
  final String agentId;
  final String name;
  final String email;
  final String phone;
  final String location;
  final VerificationStatus verificationStatus;
  final String imageUrl;
  final List<String> specialization;
  final List<String> languages;
  final double rating;
  final int experience;
  final int propertiesSold;
  final int? credits;
  final String? bio;
  final String? company;
  final String? licenseNumber;
  final DateTime? joinedDate;

  const Agent({
    required this.agentId,
    required this.name,
    required this.email,
    required this.phone,
    required this.location,
    required this.verificationStatus,
    required this.imageUrl,
    required this.specialization,
    required this.languages,
    required this.rating,
    required this.experience,
    required this.propertiesSold,
    this.credits,
    this.bio,
    this.company,
    this.licenseNumber,
    this.joinedDate,
  });

  Agent copyWith({
    String? agentId,
    String? name,
    String? email,
    String? phone,
    String? location,
    VerificationStatus? verificationStatus,
    String? imageUrl,
    List<String>? specialization,
    List<String>? languages,
    double? rating,
    int? experience,
    int? propertiesSold,
    int? credits,
    String? bio,
    String? company,
    String? licenseNumber,
    DateTime? joinedDate,
  }) {
    return Agent(
      agentId: agentId ?? this.agentId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      imageUrl: imageUrl ?? this.imageUrl,
      specialization: specialization ?? this.specialization,
      languages: languages ?? this.languages,
      rating: rating ?? this.rating,
      experience: experience ?? this.experience,
      propertiesSold: propertiesSold ?? this.propertiesSold,
      credits: credits ?? this.credits,
      bio: bio ?? this.bio,
      company: company ?? this.company,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      joinedDate: joinedDate ?? this.joinedDate,
    );
  }

  @override
  List<Object?> get props => [
        agentId,
        name,
        email,
        phone,
        location,
        verificationStatus,
        imageUrl,
        specialization,
        languages,
        rating,
        experience,
        propertiesSold,
        credits,
        bio,
        company,
        licenseNumber,
        joinedDate,
      ];

  // Helper methods
  bool get isVerified => verificationStatus == VerificationStatus.verified;
  bool get isPending => verificationStatus == VerificationStatus.pending;
  bool get isRejected => verificationStatus == VerificationStatus.rejected;
  
  String get verificationDisplayName {
    switch (verificationStatus) {
      case VerificationStatus.verified:
        return 'Verified Agent';
      case VerificationStatus.pending:
        return 'Pending Verification';
      case VerificationStatus.rejected:
        return 'Verification Rejected';
    }
  }
  
  String get experienceText {
    if (experience == 1) {
      return '1 year experience';
    } else {
      return '$experience years experience';
    }
  }
  
  String get propertiesSoldText {
    if (propertiesSold == 1) {
      return '1 property sold';
    } else {
      return '$propertiesSold properties sold';
    }
  }
  
  String get ratingText {
    return '${rating.toStringAsFixed(1)} ⭐';
  }
  
  String get specializationText {
    if (specialization.isEmpty) return 'General Real Estate';
    return specialization.join(', ');
  }
  
  String get languagesText {
    if (languages.isEmpty) return 'English';
    return languages.join(', ');
  }
  
  String get shortBio {
    if (bio == null || bio!.isEmpty) {
      return 'Experienced real estate agent specializing in ${specializationText.toLowerCase()}.';
    }
    return bio!.length > 100 ? '${bio!.substring(0, 100)}...' : bio!;
  }
}
