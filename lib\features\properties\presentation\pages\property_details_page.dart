import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';

class PropertyDetailsPage extends StatelessWidget {
  final String propertyId;

  const PropertyDetailsPage({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Property Details',
      ),
      body: Center(
        child: Text('Property ID: $propertyId'),
      ),
    );
  }
}
