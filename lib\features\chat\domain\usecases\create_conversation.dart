import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/conversation.dart';
import '../repositories/chat_repository.dart';

class CreateConversation implements UseCase<Conversation, CreateConversationParams> {
  final ChatRepository repository;

  CreateConversation(this.repository);

  @override
  Future<Either<Failure, Conversation>> call(CreateConversationParams params) async {
    return await repository.createConversation(
      title: params.title,
      userId: params.userId,
      firstMessage: params.firstMessage,
    );
  }
}

class CreateConversationParams extends Equatable {
  final String title;
  final String? userId;
  final String? firstMessage;

  const CreateConversationParams({
    required this.title,
    this.userId,
    this.firstMessage,
  });

  @override
  List<Object?> get props => [title, userId, firstMessage];
}
