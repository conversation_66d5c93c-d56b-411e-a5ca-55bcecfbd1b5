import 'package:equatable/equatable.dart';

enum MessageType {
  user,
  assistant,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  failed,
}

class Message extends Equatable {
  final String id;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final MessageStatus status;
  final String? conversationId;
  final Map<String, dynamic>? metadata;
  final bool isEdited;
  final DateTime? editedAt;
  final List<String>? attachments;
  final String? parentMessageId;

  const Message({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.status = MessageStatus.sent,
    this.conversationId,
    this.metadata,
    this.isEdited = false,
    this.editedAt,
    this.attachments,
    this.parentMessageId,
  });

  Message copyWith({
    String? id,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    MessageStatus? status,
    String? conversationId,
    Map<String, dynamic>? metadata,
    bool? isEdited,
    DateTime? editedAt,
    List<String>? attachments,
    String? parentMessageId,
  }) {
    return Message(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      conversationId: conversationId ?? this.conversationId,
      metadata: metadata ?? this.metadata,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      attachments: attachments ?? this.attachments,
      parentMessageId: parentMessageId ?? this.parentMessageId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'conversationId': conversationId,
      'metadata': metadata,
      'isEdited': isEdited,
      'editedAt': editedAt?.toIso8601String(),
      'attachments': attachments,
      'parentMessageId': parentMessageId,
    };
  }

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      content: json['content'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.user,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      conversationId: json['conversationId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isEdited: json['isEdited'] as bool? ?? false,
      editedAt: json['editedAt'] != null 
          ? DateTime.parse(json['editedAt'] as String)
          : null,
      attachments: (json['attachments'] as List<dynamic>?)?.cast<String>(),
      parentMessageId: json['parentMessageId'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        content,
        type,
        timestamp,
        status,
        conversationId,
        metadata,
        isEdited,
        editedAt,
        attachments,
        parentMessageId,
      ];
}
