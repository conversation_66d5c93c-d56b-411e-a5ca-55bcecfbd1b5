part of 'agents_bloc.dart';

abstract class AgentsEvent extends Equatable {
  const AgentsEvent();

  @override
  List<Object> get props => [];
}

class AgentsLoadRequested extends AgentsEvent {
  const AgentsLoadRequested();
}

class AgentDetailsLoadRequested extends AgentsEvent {
  final String agentId;

  const AgentDetailsLoadRequested({required this.agentId});

  @override
  List<Object> get props => [agentId];
}
