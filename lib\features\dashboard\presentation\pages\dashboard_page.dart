import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          return _buildResponsiveDashboard(context, state.user);
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildResponsiveDashboard(BuildContext context, user) {
    return ResponsiveLayout(
      mobile: _buildMobileDashboard(context, user),
      desktop: _buildDesktopDashboard(context, user),
    );
  }

  Widget _buildMobileDashboard(BuildContext context, user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Welcome Home',
            mobileSize: 24,
            fontWeight: FontWeight.w800,
            color: AppColors.onBackground,
          ),
          const SizedBox(height: 24),
          _buildQuickStats(context, user),
          const SizedBox(height: 24),
          _buildRecommendedProperties(context, user),
          const SizedBox(height: 24),
          _buildRecentActivity(context, user),
        ],
      ),
    );
  }

  Widget _buildDesktopDashboard(BuildContext context, user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Welcome Home',
            mobileSize: 32,
            desktopSize: 40,
            fontWeight: FontWeight.w800,
            color: AppColors.onBackground,
          ),
          const SizedBox(height: 32),

          // Desktop Grid Layout
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main Content (2/3)
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    _buildQuickStats(context, user),
                    const SizedBox(height: 32),
                    _buildRecommendedProperties(context, user),
                  ],
                ),
              ),

              const SizedBox(width: 32),

              // Sidebar (1/3)
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    _buildRecentActivity(context, user),
                    const SizedBox(height: 24),
                    _buildQuickActions(context, user),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, user) {
    final stats = [
      {'title': 'Saved Properties', 'value': '12', 'icon': Icons.favorite, 'color': AppColors.primary},
      {'title': 'Property Views', 'value': '48', 'icon': Icons.visibility, 'color': AppColors.success},
      {'title': 'Market Value', 'value': '\$2.4M', 'icon': Icons.trending_up, 'color': AppColors.warning},
      {'title': 'Active Searches', 'value': '3', 'icon': Icons.search, 'color': AppColors.secondary},
    ];

    return ResponsiveGrid(
      mobileColumns: 2,
      desktopColumns: 4,
      spacing: 16,
      children: stats.map((stat) => _buildStatCard(context, stat)).toList(),
    );
  }

  Widget _buildStatCard(BuildContext context, Map<String, dynamic> stat) {
    return EnhancedCard(
      padding: EdgeInsets.all(context.isDesktop ? 24 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: (stat['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  stat['icon'] as IconData,
                  color: stat['color'] as Color,
                  size: context.isDesktop ? 24 : 20,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.more_vert,
                color: AppColors.onSurfaceVariant,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 16),
          ResponsiveText(
            stat['value'] as String,
            mobileSize: 24,
            desktopSize: 28,
            fontWeight: FontWeight.w700,
            color: AppColors.onSurface,
          ),
          const SizedBox(height: 4),
          ResponsiveText(
            stat['title'] as String,
            mobileSize: 12,
            desktopSize: 14,
            color: AppColors.onSurfaceVariant,
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedProperties(BuildContext context, user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Recommended for You',
          mobileSize: 20,
          desktopSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.onBackground,
        ),
        const SizedBox(height: 16),
        ResponsiveGrid(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: context.isLargeDesktop ? 3 : 2,
          spacing: 16,
          children: List.generate(4, (index) => _buildPropertyCard(context, index)),
        ),
      ],
    );
  }

  Widget _buildPropertyCard(BuildContext context, int index) {
    return EnhancedCard(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Image
          Container(
            height: context.isDesktop ? 200 : 160,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              color: AppColors.surfaceVariant,
            ),
            child: Center(
              child: Icon(
                Icons.home,
                size: 48,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  '\$${(500 + index * 100)}K',
                  mobileSize: 18,
                  desktopSize: 20,
                  fontWeight: FontWeight.w700,
                  color: AppColors.primary,
                ),
                const SizedBox(height: 4),
                ResponsiveText(
                  '${2 + index} BR • ${2 + index} BA • ${1200 + index * 200} sqft',
                  mobileSize: 12,
                  desktopSize: 14,
                  color: AppColors.onSurfaceVariant,
                ),
                const SizedBox(height: 8),
                ResponsiveText(
                  'Beautiful ${index % 2 == 0 ? 'apartment' : 'villa'} in prime location',
                  mobileSize: 14,
                  desktopSize: 16,
                  color: AppColors.onSurface,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(BuildContext context, user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Recent Activity',
          mobileSize: 20,
          desktopSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.onBackground,
        ),
        const SizedBox(height: 16),
        EnhancedCard(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: List.generate(3, (index) => _buildActivityItem(context, index)),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(BuildContext context, int index) {
    final activities = [
      {'title': 'Property Saved', 'subtitle': 'Luxury Villa in Marina', 'icon': Icons.favorite},
      {'title': 'Search Alert', 'subtitle': 'New properties match your criteria', 'icon': Icons.notifications},
      {'title': 'Price Update', 'subtitle': 'Downtown Apartment price reduced', 'icon': Icons.trending_down},
    ];

    final activity = activities[index];

    return Padding(
      padding: EdgeInsets.only(bottom: index < 2 ? 16 : 0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity['icon'] as IconData,
              color: AppColors.primary,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  activity['title'] as String,
                  mobileSize: 14,
                  desktopSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
                ResponsiveText(
                  activity['subtitle'] as String,
                  mobileSize: 12,
                  desktopSize: 14,
                  color: AppColors.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Quick Actions',
          mobileSize: 20,
          desktopSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.onBackground,
        ),
        const SizedBox(height: 16),
        Column(
          children: [
            _buildActionButton(
              context,
              'Find Properties',
              Icons.search,
              AppColors.primary,
              () {},
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              context,
              'Get Valuation',
              Icons.assessment,
              AppColors.secondary,
              () {},
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              context,
              'Contact Agent',
              Icons.person,
              AppColors.success,
              () {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return EnhancedCard(
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ResponsiveText(
              title,
              mobileSize: 14,
              desktopSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: AppColors.onSurfaceVariant,
          ),
        ],
      ),
    );
  }

  Widget _buildOldDashboardContent(BuildContext context, user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: NetworkImage(user.imageUrl),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back, ${user.name}!',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Ready to find your perfect property?',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Quick Actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildQuickActionCard(
                context,
                'Find Properties',
                Icons.search,
                AppColors.primary,
                () {
                  // Navigate to property finder
                },
              ),
              _buildQuickActionCard(
                context,
                'Find Agents',
                Icons.people,
                AppColors.success,
                () {
                  // Navigate to agents
                },
              ),
              _buildQuickActionCard(
                context,
                'Saved Properties',
                Icons.favorite,
                AppColors.warning,
                () {
                  // Navigate to saved properties
                },
              ),
              _buildQuickActionCard(
                context,
                'Notifications',
                Icons.notifications,
                AppColors.info,
                () {
                  // Navigate to notifications
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Recent Activity
          Text(
            'Recent Activity',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.primaryLight,
                      child: Icon(
                        Icons.home,
                        color: AppColors.primary,
                      ),
                    ),
                    title: const Text('New property match found'),
                    subtitle: const Text('3 bedroom house in Downtown'),
                    trailing: const Text('2h ago'),
                  ),
                  const Divider(),
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.successLight,
                      child: Icon(
                        Icons.person,
                        color: AppColors.success,
                      ),
                    ),
                    title: const Text('Agent responded to inquiry'),
                    subtitle: const Text('Omar Hassan replied to your message'),
                    trailing: const Text('5h ago'),
                  ),
                  const Divider(),
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.infoLight,
                      child: Icon(
                        Icons.trending_up,
                        color: AppColors.info,
                      ),
                    ),
                    title: const Text('Market update available'),
                    subtitle: const Text('Property values increased by 2.3%'),
                    trailing: const Text('1d ago'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
