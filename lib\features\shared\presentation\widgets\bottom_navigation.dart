import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../auth/domain/entities/user.dart';

class BottomNavigation extends StatelessWidget {
  final User user;

  const BottomNavigation({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final currentRoute = GoRouterState.of(context).uri.path;
    final navItems = _getNavigationItemsForRole(user.userRole, user.verificationStatus);
    
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: navItems.map((item) {
              final isSelected = currentRoute == item['route'];
              
              return Expanded(
                child: InkWell(
                  onTap: () => context.go(item['route'] as String),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          item['icon'] as IconData,
                          size: 24,
                          color: isSelected 
                              ? AppColors.primary 
                              : AppColors.onSurfaceVariant,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          item['label'] as String,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: isSelected 
                                ? AppColors.primary 
                                : AppColors.onSurfaceVariant,
                            fontWeight: isSelected 
                                ? FontWeight.w600 
                                : FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getNavigationItemsForRole(
    UserRole role, 
    VerificationStatus? verificationStatus,
  ) {
    switch (role) {
      case UserRole.admin:
        return [
          {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/admin'},
          {'icon': Icons.people, 'label': 'Users', 'route': '/admin/users'},
          {'icon': Icons.settings, 'label': 'Features', 'route': '/admin/features'},
          {'icon': Icons.verified_user, 'label': 'Agents', 'route': '/admin/agent-verification'},
        ];
      
      case UserRole.agent:
        if (verificationStatus == VerificationStatus.verified) {
          return [
            {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/agent-dashboard'},
            {'icon': Icons.assignment, 'label': 'Leads', 'route': '/agent-leads'},
            {'icon': Icons.search, 'label': 'Finder', 'route': '/property-finder'},
            {'icon': Icons.home_work, 'label': 'Listings', 'route': '/manage-properties'},
            {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
          ];
        } else {
          return [
            {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/dashboard'},
            {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
          ];
        }
      
      case UserRole.investor:
        return [
          {'icon': Icons.dashboard, 'label': 'Dashboard', 'route': '/investor-dashboard'},
          {'icon': Icons.home_work, 'label': 'Properties', 'route': '/manage-properties'},
          {'icon': Icons.notifications, 'label': 'Alerts', 'route': '/notifications'},
          {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
        ];
      
      case UserRole.regular:
        return [
          {'icon': Icons.dashboard, 'label': 'Home', 'route': '/dashboard'},
          {'icon': Icons.search, 'label': 'Finder', 'route': '/property-finder'},
          {'icon': Icons.people, 'label': 'Agents', 'route': '/agents'},
          {'icon': Icons.notifications, 'label': 'Alerts', 'route': '/notifications'},
          {'icon': Icons.person, 'label': 'Profile', 'route': '/profile'},
        ];
    }
  }
}
