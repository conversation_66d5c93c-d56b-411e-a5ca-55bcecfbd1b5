import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/agent.dart';
import '../repositories/agents_repository.dart';

class GetAgents implements UseCase<List<Agent>, NoParams> {
  final AgentsRepository repository;

  GetAgents(this.repository);

  @override
  Future<Either<Failure, List<Agent>>> call(NoParams params) async {
    return await repository.getAgents();
  }
}
