import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/error_widget.dart';

class UserManagementPage extends StatelessWidget {
  const UserManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'User Management',
      ),
      body: const Center(
        child: EmptyStateWidget(
          title: 'User Management',
          message: 'User management features will be available here.',
          icon: Icons.people_outlined,
        ),
      ),
    );
  }
}
