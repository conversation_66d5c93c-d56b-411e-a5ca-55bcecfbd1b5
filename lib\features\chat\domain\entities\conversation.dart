import 'package:equatable/equatable.dart';
import 'message.dart';

enum ConversationStatus {
  active,
  archived,
  deleted,
}

class Conversation extends Equatable {
  final String id;
  final String title;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ConversationStatus status;
  final List<Message> messages;
  final bool isPinned;
  final bool isFavorite;
  final Map<String, dynamic>? metadata;
  final String? userId;
  final int messageCount;
  final String? lastMessagePreview;
  final DateTime? lastMessageAt;
  final List<String>? tags;

  const Conversation({
    required this.id,
    required this.title,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.status = ConversationStatus.active,
    this.messages = const [],
    this.isPinned = false,
    this.isFavorite = false,
    this.metadata,
    this.userId,
    required this.messageCount,
    this.lastMessagePreview,
    this.lastMessageAt,
    this.tags,
  });

  Conversation copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    ConversationStatus? status,
    List<Message>? messages,
    bool? isPinned,
    bool? isFavorite,
    Map<String, dynamic>? metadata,
    String? userId,
    int? messageCount,
    String? lastMessagePreview,
    DateTime? lastMessageAt,
    List<String>? tags,
  }) {
    return Conversation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      messages: messages ?? this.messages,
      isPinned: isPinned ?? this.isPinned,
      isFavorite: isFavorite ?? this.isFavorite,
      metadata: metadata ?? this.metadata,
      userId: userId ?? this.userId,
      messageCount: messageCount ?? this.messageCount,
      lastMessagePreview: lastMessagePreview ?? this.lastMessagePreview,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status.name,
      'messages': messages.map((m) => m.toJson()).toList(),
      'isPinned': isPinned,
      'isFavorite': isFavorite,
      'metadata': metadata,
      'userId': userId,
      'messageCount': messageCount,
      'lastMessagePreview': lastMessagePreview,
      'lastMessageAt': lastMessageAt?.toIso8601String(),
      'tags': tags,
    };
  }

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      status: ConversationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ConversationStatus.active,
      ),
      messages: (json['messages'] as List<dynamic>?)
              ?.map((m) => Message.fromJson(m as Map<String, dynamic>))
              .toList() ??
          [],
      isPinned: json['isPinned'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
      userId: json['userId'] as String?,
      messageCount: json['messageCount'] as int? ?? 0,
      lastMessagePreview: json['lastMessagePreview'] as String?,
      lastMessageAt: json['lastMessageAt'] != null
          ? DateTime.parse(json['lastMessageAt'] as String)
          : null,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
    );
  }

  /// Create a new conversation with a generated title based on first message
  factory Conversation.create({
    required String id,
    String? title,
    String? userId,
    String? firstMessage,
  }) {
    final now = DateTime.now();
    return Conversation(
      id: id,
      title: title ?? _generateTitle(firstMessage),
      createdAt: now,
      updatedAt: now,
      userId: userId,
      messageCount: 0,
    );
  }

  static String _generateTitle(String? firstMessage) {
    if (firstMessage == null || firstMessage.isEmpty) {
      return 'New Conversation';
    }
    
    // Extract first few words for title
    final words = firstMessage.split(' ').take(5).join(' ');
    return words.length > 30 ? '${words.substring(0, 30)}...' : words;
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        createdAt,
        updatedAt,
        status,
        messages,
        isPinned,
        isFavorite,
        metadata,
        userId,
        messageCount,
        lastMessagePreview,
        lastMessageAt,
        tags,
      ];
}
