import 'package:dio/dio.dart';
import '../../../../core/services/mock_data_service.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> loginUser(String email, String password);
  Future<void> logoutUser();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final Dio dio;
  final MockDataService mockDataService = MockDataService();

  AuthRemoteDataSourceImpl({required this.dio});

  @override
  Future<UserModel> loginUser(String email, String password) async {
    // In a real app, this would make an HTTP request
    // For now, we'll use the mock data service
    final user = await mockDataService.authenticateUser(email, password);
    
    if (user == null) {
      throw Exception('Invalid credentials');
    }
    
    return UserModel.fromEntity(user);
  }

  @override
  Future<void> logoutUser() async {
    // In a real app, this would make an HTTP request to logout
    // For now, we'll just simulate a delay
    await Future.delayed(const Duration(milliseconds: 200));
  }
}
